import { Component, EventEmitter, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { PROP_DATE_TYPE } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { changeCalendar, onPickerOpened } from 'src/app/core/utils/common.util';
import { FetchChannelPartnerLocations } from 'src/app/reducers/manage-marketing/marketing.action';
import { getChannelPartnerLoationList, getIsChannelPartnerLocationLoading } from 'src/app/reducers/manage-marketing/marketing.reducer';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'channel-partner-advance-filter',
  templateUrl: './channel-partner-advance-filter.component.html',
})
export class ChannelPartnerAdvanceFilterComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  applyAdvancedFilter: () => void;
  onClearAllFilters: (data: any)=> void;
  appliedFilter:any;
  dateTypeList: Array<string> = PROP_DATE_TYPE.slice(0, 3);
  locationsIsLoading: boolean = true;
  locations: string[];
  propertyList: Array<string> = ['0-50' , '50-100' , 'Above 100'];
  onPickerOpened = onPickerOpened
  currentDate: Date = new Date();
  userData: any;

  constructor(
    public modalRef: BsModalRef,
    private modalService: BsModalService,
    private _store: Store<AppState>,
  ) { 
    this._store.dispatch(new FetchChannelPartnerLocations());
    // this._store.dispatch(new FetchPropertyWithIdNameList());
  }

  ngOnInit(): void {

    this._store
    .select(getUserBasicDetails)
    .pipe(takeUntil(this.stopper))
    .subscribe((data: any) => {
      this.userData = data;
      this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset)
    });

    this._store
      .select(getIsChannelPartnerLocationLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.locationsIsLoading = data;
      });

    this._store
    .select(getChannelPartnerLoationList)
    .pipe(takeUntil(this.stopper))
    .subscribe((data: any) => {
      this.locations = data
        .filter((data: any) => data)
        .slice()
        .sort((a: any, b: any) => a.localeCompare(b));
    });

  }


  onResetDateFilter() {
    this.appliedFilter = {
      ...this.appliedFilter,
      DateType: null,
      date: '',
    };
  }

}
