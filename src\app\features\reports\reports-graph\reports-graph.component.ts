import { Component, Input, OnChanges, SimpleChanges, ElementRef, EventEmitter, Output, ViewChild, HostListener } from '@angular/core';
import { Store } from '@ngrx/store';

// Third-party libraries
import { Chart, ChartTypeRegistry, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, LineController } from 'chart.js';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import * as html2canvas from 'html2canvas';
import * as ExcelJS from 'exceljs';
import { Subject, takeUntil } from 'rxjs';

// Local services and utilities
import { ReportsGraphStateService } from '../../../services/shared/reports-graph-state.service';
import { changeCalendar, getSystemTimeOffset, getSystemTimeZoneId, getTimeZoneDate } from 'src/app/core/utils/common.util';
import { AppState } from 'src/app/app.reducer';
import { getUserBasicDetails, getUsersListForReassignment, getUsersListForReassignmentIsLoading } from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { LeadSource, FolderNamesS3, ReportDateType, OwnerSelectionType, UserStatus } from 'src/app/app.enum';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { environment as env } from 'src/environments/environment';
import { REPORT_FILTERS_KEY_LABEL } from 'src/app/app.constants';

Chart.register(LineController, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend);

interface ChartColumn {
  field: string;
  headerName: string;
  parentName?: string;
  uniqueField?: string;
  displayName?: string;
  originalField?: string;
  valueGetter?: Function;
  valueIndex?: number;
  valueLabels?: string[];
}

// Utility function for safe value access
function safeGet<T>(value: T | undefined | null, defaultValue: T): T {
  return value == null ? defaultValue : value;
}

@Component({
  selector: 'report-graph',
  templateUrl: './reports-graph.component.html',
})
export class ReportGraphComponent implements OnChanges {
  @ViewChild('activityChart') chartCanvas: ElementRef;
  @Input() rowData: any[] = [];
  @Input() gridOptions: any;
  @Input() filteredColumnDefsCache: any[] = [];
  @Input() xAxisData: any;
  @Input() reportType: string = 'default';
  @Input() type: string = 'default';
  @Input() userData: any;
  @Input() payload: any;
  @Output() exportTriggered = new EventEmitter<void>();
  @Output() exportStarted = new EventEmitter<void>();
  @Output() exportFinished = new EventEmitter<void>();
  @Input() getArrayOfFilters: (key: string, value: any) => string[];
  allUsers: any[] = [];
  isAllUsersLoading: boolean = true;

  parentColumns: any[] = [];
  childColumns: ChartColumn[] = [];
  selectedParent: any[] = [];
  selectedChildren: ChartColumn[] = [];

  selectedChartType: keyof ChartTypeRegistry = 'line';
  chart: Chart | null = null;
  isChartReady: boolean = false;
  noDataAvailable: boolean = false;
  showSelectionMessage: boolean = false;
  getTimeZoneDate = getTimeZoneDate;
  s3BucketUrl: string = env.s3ImageBucketURL;
  reportFiltersKeyLabel = REPORT_FILTERS_KEY_LABEL;

  readonly chartTypes = [
    { label: 'Line Chart', value: 'line' },
    { label: 'Bar Chart', value: 'bar' },
    // { label: 'Radar Chart', value: 'radar' },
  ] as const;

  currentDate: Date = new Date();
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  isExporting = false;

  constructor(
    public modalService: BsModalService,
    private graphStateService: ReportsGraphStateService,
    private store: Store<AppState>,
    private headerTitle: HeaderTitleService,
    private s3UploadService: BlobStorageService
  ) {
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });
    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUsers = (data || []).map((user: any) => ({
          ...user,
          fullName: user.firstName + ' ' + user.lastName,
        }));
        this.allUsers = this.allUsers.sort((a: any, b: any) => a.fullName.localeCompare(b.fullName));
      });
    this.store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAllUsersLoading = isLoading;
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.filteredColumnDefsCache && this.filteredColumnDefsCache?.length > 0) {
      this.initializeColumnsAndLoadSelections();
    } else if (changes.rowData || changes.selectedChartType) {
      this.updateChartWithDelay();
    }
  }

  initializeColumnsAndLoadSelections(): void {
    this.setupColumns();
    if (this.graphStateService.isInitialized(this.reportType)) {
      this.loadSavedSelections();
    } else {
      this.initializeDefaultSelections();
    }
    this.updateChartWithDelay();
  }

  setupColumns(): void {
    this.parentColumns = [];
    this.childColumns = [];
    const hasParentColumns = this.filteredColumnDefsCache.some(col => col.children?.length);
    if (hasParentColumns) {
      this.parentColumns = this.filteredColumnDefsCache;
    } else {
      this.childColumns = this.filteredColumnDefsCache.map(col => ({
        ...col,
        parentName: col.headerName,
        displayName: col.headerName
      }));
    }
  }

  loadSavedSelections(): void {
    const savedSelections = this.graphStateService.getSelections(this.reportType);
    this.selectedParent = Array.isArray(savedSelections.selectedParent)
      ? savedSelections.selectedParent
      : savedSelections.selectedParent ? [savedSelections.selectedParent] : [];
    this.selectedChildren = savedSelections.selectedChildren;
    this.selectedChartType = savedSelections.selectedChartType as keyof ChartTypeRegistry;
    this.childColumns = savedSelections.childColumns;
    this.updateSelectionState();
  }

  initializeDefaultSelections(): void {
    const regularColumns = this.filteredColumnDefsCache.filter(col => !col.isParent);
    if (this.parentColumns.length > 0) {
      this.selectedParent = [...this.parentColumns];
      this.childColumns = this.parentColumns.reduce((acc: ChartColumn[], parent) => acc.concat(this.createChartColumnsFromParent(parent)), []);
      this.selectedChildren = this.childColumns.length ? [...this.childColumns] : [];
    } else {
      this.childColumns = regularColumns.flatMap(col => {
        if (col.valueLabels && Array.isArray(col.valueLabels)) {
          return col.valueLabels.map((label: string, idx: number) => ({
            ...col,
            parentName: col.headerName,
            uniqueField: `${col.headerName}_${col.field}_${label.replace(/\s+/g, '_').toLowerCase()}`,
            displayName: label,
            originalField: col.field,
            valueIndex: idx,
          }));
        }
        // If no valueLabels, treat as a single field (even if valueGetter returns an array)
        return {
          ...col,
          uniqueField: `${col.headerName}_${col.field}`,
          displayName: col.headerName,
          valueIndex: 0, // Always use the first value
        };
      });
      this.selectedChildren = this.childColumns.length ? [...this.childColumns] : [];
      this.selectedParent = [];
    }
    this.saveSelectionsToService(regularColumns);
    this.updateSelectionState();
  }

  createChartColumnsFromParent(parent: any): ChartColumn[] {
    // If valueLabels exists on parent, create a child for each label
    if (parent.valueLabels && Array.isArray(parent.valueLabels)) {
      return parent.valueLabels.map((label: string, idx: number) => ({
        ...parent,
        parentName: parent.headerName,
        uniqueField: `${parent.headerName}_${parent.field}_${label.replace(/\s+/g, '_').toLowerCase()}`,
        displayName: label,
        originalField: parent.field,
        valueIndex: idx, // Used to select the correct value from the array
      }));
    }
    // If parent has children, check if children have valueLabels
    if (parent?.children?.length) {
      return parent.children.flatMap((child: any) => {
        if (child.valueLabels && Array.isArray(child.valueLabels)) {
          return child.valueLabels.map((label: string, idx: number) => ({
            ...child,
            parentName: parent.headerName,
            uniqueField: `${parent.headerName}_${child.field}_${label.replace(/\s+/g, '_').toLowerCase()}`,
            displayName:  `${label} (${parent.headerName})`,
            originalField: child.field,
            valueIndex: idx
          }));
        }
        // Fallback: just the child itself
        return {
          ...child,
          parentName: parent.headerName,
          uniqueField: `${parent.headerName}_${child.field}`,
          displayName: child.headerName === parent.headerName ? child.headerName : `${child.headerName} (${parent.headerName})`,
          originalField: child.field,
          valueIndex: 0,
        };
      });
    }
    return [];
  }

  saveSelectionsToService(regularColumns: any[]): void {
    this.graphStateService.initializeSelections(this.reportType, this.parentColumns, regularColumns);
    this.graphStateService.updateParentSelection(this.reportType, this.selectedParent, this.childColumns);
    this.graphStateService.updateChildrenSelection(this.reportType, this.selectedChildren);
  }

  updateChartWithDelay(): void {
    this.isChartReady = false;
    setTimeout(() => this.initializeChart(), 300);
  }

  validateSelections(): boolean {
    return this.parentColumns.length > 0
      ? this.selectedParent?.length > 0 && this.selectedChildren?.length > 0
      : this.selectedChildren?.length > 0;
  }

  updateSelectionState(): void {
    const hasValidSelections = this.validateSelections();
    this.showSelectionMessage = !hasValidSelections;
    if (!hasValidSelections) {
      this.isChartReady = false;
      this.noDataAvailable = false;
      this.destroyExistingChart();
    }
  }

  findColumnByField(field: string): any {
    if (!field) return null;
    const foundInCache = this.searchInColumns(this.filteredColumnDefsCache ?? [], field);
    if (foundInCache) return foundInCache;
    if (this.gridOptions?.columnDefs) {
      return this.searchInColumns(this.gridOptions.columnDefs ?? [], field);
    }
    return null;
  }

  searchInColumns(columns: any[] = [], field: string): any {
    for (const col of columns ?? []) {
      if (col?.field === field) return col;
      if (col?.children?.length) {
        const childCol = (col.children ?? []).find((child: any) => child?.field === field);
        if (childCol) return childCol;
      }
    }
    return null;
  }

  filterTotalRows(rows: any[] = []): any[] {
    if (!rows?.length) return [];
    const totalFields = [
      'userName', 'projectTitle', 'name', 'firstName',
      'source', 'subSource', 'agencyName', 'city',
      'status', 'subStatus', 'location'
    ];
    return rows.filter(row => row && !totalFields.some(field => row?.[field] === 'Total'));
  }

  getXAxisLabel(row: any): string {
    if (!this.xAxisData || !row) return '';
    let label = '';
    if (this.type === 'SourceEnum' && row?.[this.xAxisData] != null) {
      label = LeadSource?.[row[this.xAxisData]] ?? row[this.xAxisData];
    } else if (this.type === 'DateFormat' && row?.[this.xAxisData]) {
      label = getTimeZoneDate(row[this.xAxisData], this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear');
    } else if (this.xAxisData.includes(' ')) {
      label = this.xAxisData.split(' ')
        .map((field: string) => safeGet(row?.[field], ''))
        .filter(Boolean)
        .join(' ');
    } else if (this.xAxisData.includes('|')) {
      const fields = this.xAxisData.split('|');
      for (const field of fields) {
        const value = this.getNestedValue(row, field.trim());
        if (value != null) {
          label = value;
          break;
        }
      }
    } else if (this.xAxisData.includes('.')) {
      label = safeGet(this.getNestedValue(row, this.xAxisData), '');
    } else {
      label = safeGet(row?.[this.xAxisData], '');
    }
    return label;
  }

  getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, prop) => current?.[prop], obj);
  }

  initializeChart(): void {
    this.updateSelectionState();
    if (this.showSelectionMessage) {
      this.destroyExistingChart();
      return;
    }
    const ctx = document.getElementById('activityChart') as HTMLCanvasElement;
    if (!ctx) {
      this.isChartReady = false;
      return;
    }
    this.destroyExistingChart();
    const filteredRowData = this.filterTotalRows(this.rowData);
    if (!filteredRowData?.length) {
      this.showNoDataState();
      const legendContainer = document.getElementById('chart-legend-container');
      if (legendContainer) legendContainer.innerHTML = '';
      return;
    }
    this.noDataAvailable = false;
    this.showSelectionMessage = false;
    const datasets = this.createChartDatasets(filteredRowData);
    const labels = this.createChartLabels(filteredRowData);
    this.chart = new Chart(ctx, {
      type: this.selectedChartType,
      data: { labels, datasets },
      options: this.getChartOptions(),
    });
    this.isChartReady = true;
    const legendContainer = document.getElementById('chart-legend-container');
    if (legendContainer && this.chart) {
      legendContainer.innerHTML = this.generateCustomLegend();
    }
  }

  generateCustomLegend(): string {
    if (!this.chart?.data?.datasets) return '';
    return `<div class="d-flex flex-wrap w-100 justify-center">` +
      this.chart.data.datasets.map((ds: any, i: number) => {
        const color = Array.isArray(ds.backgroundColor) ? ds.backgroundColor[0] : ds.backgroundColor;
        return `<div class="d-flex align-center m-4">
          <span style='display: inline-block; width: 12px; height: 12px; border-radius: 50%; background: ${color}; margin: 6px'></span>
          <span class="text-large">${ds.label}</span>
        </div>`;
      }).join('') + `</div>`;
  }

  destroyExistingChart(): void {
    if (this.chart) {
      this.chart.destroy();
      this.chart = null;
    }
    const chartInstance = (window as any).Chart?.getChart
      ? (window as any).Chart.getChart('activityChart')
      : (Chart as any).getChart?.('activityChart');
    if (chartInstance) chartInstance.destroy();
    const ctx = document.getElementById('activityChart') as HTMLCanvasElement;
    if (ctx) ctx.getContext('2d')?.clearRect(0, 0, ctx.width, ctx.height);
    const legendContainer = document.getElementById('chart-legend-container');
    if (legendContainer) legendContainer.innerHTML = '';
  }

  showNoDataState(): void {
    this.noDataAvailable = true;
    this.isChartReady = false;
    this.showSelectionMessage = false;
    const legendContainer = document.getElementById('chart-legend-container');
    if (legendContainer) legendContainer.innerHTML = '';
  }

  createChartLabels(filteredRowData: any[]): string[] {
    return filteredRowData.map(row => {
      const label = this.getXAxisLabel(row);
      return label.length > 15 ? label.substring(0, 15) + "..." : label;
    });
  }

  createChartDatasets(filteredRowData: any[]): any[] {
    const datasets: any[] = [];
    this.selectedChildren.forEach((child) => {
      const fieldToUse = child.originalField || child.field;
      const col = this.findColumnByField(fieldToUse);
      const allValues = (filteredRowData ?? []).map(row => {
        let value = col?.valueGetter && typeof col.valueGetter === 'function'
          ? col.valueGetter({ data: row })
          : row?.[col?.field];
        return value;
      });
      // Always use valueIndex (default 0 if not set)
      const idx = typeof child.valueIndex === 'number' ? child.valueIndex : 0;
      datasets.push({
        label: child.displayName || col?.headerName || col?.field,
        data: allValues.map(val => Array.isArray(val) ? (val[idx] ?? 0) : val ?? 0),
        backgroundColor: this.getChartColor(datasets.length, 0.5),
        borderColor: this.getChartColor(datasets.length, 1),
        borderWidth: 1,
      });
    });
    return datasets;
  }

  getChartOptions(): any {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        tooltip: {
          mode: 'index',
          intersect: false,
          bodyFont: { family: 'Lexend Deca, DM Sans, sans-serif' },
          titleFont: { family: 'Lexend Deca, DM Sans, sans-serif' },
          callbacks: {
            label: function (context: any) {
              const value = context.parsed && typeof context.parsed === 'object' && 'y' in context.parsed
                ? context.parsed.y
                : context.parsed;
              return value === 0 ? null : `${context.dataset.label}: ${value}`;
            }
          }
        },
        legend: { display: false }
      },
      scales: {
        x: { beginAtZero: true, ticks: { font: { size: 11, family: 'Lexend Deca, DM Sans, sans-serif' } } },
        y: { beginAtZero: true, ticks: { font: { size: 11, family: 'Lexend Deca, DM Sans, sans-serif' } } },
      },
      animation: {
        duration: 800,
        easing: 'easeOutQuart',
      },
    };
  }

  @HostListener('window:resize')
  onResize() {
    this.chart?.resize();
  }

  onParentSelectionChange(selectedParents: any[] = []): void {
    if (selectedParents?.length) {
      this.selectedParent = selectedParents;
      this.childColumns = [];
      selectedParents.forEach((parent) => {
        this.childColumns = [
          ...this.childColumns,
          ...this.createChartColumnsFromParent(parent)
        ];
      });
      this.selectedChildren = (this.selectedChildren ?? []).filter(selected =>
        (this.childColumns ?? []).some(available =>
          available?.uniqueField === selected?.uniqueField ||
          (available?.field === selected?.field && available?.parentName === selected?.parentName)
        )
      );
      this.graphStateService.updateParentSelection(this.reportType, selectedParents, this.childColumns);
      this.initializeChart();
    } else {
      this.childColumns = [];
      this.selectedChildren = [];
      this.destroyExistingChart();
      this.updateSelectionState();
    }
  }

  onChildrenSelectionChange(selectedChildren: any[] = []): void {
    this.selectedChildren = selectedChildren ?? [];
    this.graphStateService.updateChildrenSelection(this.reportType, this.selectedChildren);
    if (!(this.selectedChildren?.length)) this.destroyExistingChart();
    this.initializeChart();
  }

  onChartTypeSelectionChange(chartTypeEvent: any): void {
    this.selectedChartType = chartTypeEvent.value;
    this.graphStateService.updateChartType(this.reportType, chartTypeEvent.value);
    this.updateChartWithDelay();
  }

  getChartColor(index: number, opacity: number): string {
    const hue = (index * 137.508) % 360;
    const saturation = 65 + (index % 4) * 8;
    const lightness = 45 + (index % 5) * 6;
    const h = hue / 360;
    const s = Math.min(saturation, 90) / 100;
    const l = Math.min(lightness, 70) / 100;
    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1 / 6) return p + (q - p) * 6 * t;
      if (t < 1 / 2) return q;
      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
      return p;
    };
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    const r = Math.round(hue2rgb(p, q, h + 1 / 3) * 255);
    const g = Math.round(hue2rgb(p, q, h) * 255);
    const b = Math.round(hue2rgb(p, q, h - 1 / 3) * 255);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }

  compareByUniqueField(item1: any, item2: any): boolean {
    return item1?.uniqueField === item2?.uniqueField;
  }

  getBindLabel(): string {
    // Always use displayName since we set it in initializeColumns and initializeDefaultSelections
    return 'displayName';
  }

  getBindValue(): string {
    // If parentColumns exist, use uniqueField, else use field
    return this.parentColumns.length ? 'uniqueField' : 'field';
  }


  getCompareFunction(): (item1: any, item2: any) => boolean {
    // Always compare by uniqueField for child columns with valueLabels
    return this.compareByUniqueField;
  }

  getDisplayLabel(item: any): string {
    return item.displayName || item.headerName;
  }

  // Helper function to convert column index to Excel column letter
  getExcelColumnLetter(index: number): string {
    let result = '';
    while (index >= 0) {
      result = String.fromCharCode(65 + (index % 26)) + result;
      index = Math.floor(index / 26) - 1;
    }
    return result;
  }

  // Helper: dynamic filter formatting
  formatFilterValue(key: string, values: any): string[] {
    if (!key) return [];

    // Only keys actually present in report filter forms/payloads
    const userKeys = [
      'UserIds',
    ];
    const dateTypeKeys = [
      'dateType'
    ];

    const DateKeys = [
      'fromDate', 'toDate', 'toDateForAgency', 'fromDateForAgency', 'callLogFromDate', 'callLogToDate', 'fromDateForProject', 'toDateForProject', 'fromDateForLeadReceived', 'toDateForLeadReceived', 'fromDateForSource', 'toDateForSource', 'fromDateForSubSource', 'toDateForSubSource', 'toDateForMeetingOrVisit', 'fromDateForMeetingOrVisit'
    ];
    const userStatusKey = [
      'userStatus'
    ]

    const ownerTypeKey = [
      'ownerSelection'
    ]
    // OwnerSelectionType
    // User name mapping (exact key match)
    if (userKeys.includes(key)) {
      const arr = Array.isArray(values) ? values : [values];
      return arr.map((id: string) => this.getUserDisplayName(id));
    }

    // DateType enum mapping (exact key match)
    if (dateTypeKeys.includes(key)) {
      const label = ReportDateType[values];
      return [label];
    }

    if (ownerTypeKey.includes(key)) {
      const label = OwnerSelectionType[values];
      return [label];
    }

    if (userStatusKey.includes(key)) {
      const label = UserStatus[values];
      return [label];
    }


    if (DateKeys.includes(key) && values) {
      const arr = Array.isArray(values) ? values : [values];
      return arr.map((v: string) => getTimeZoneDate(new Date(v), this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear'));
    }

    if (Array.isArray(values)) {
      return values.map(v => v && v.toString()).filter(Boolean);
    }

    return [values?.toString()];
  }

  calculateTotalsForSelectedColumns(): any {
    if (!this.selectedChildren?.length || !this.rowData?.length) {
      return {};
    }

    const totals: any = {};

    this.selectedChildren.forEach((child) => {
      const fieldToUse = child.originalField || child.field;
      const col = this.findColumnByField(fieldToUse);

      if (!col) return;

      // Calculate total for this column using the same logic as ag-grid
      let total = 0;
      this.rowData.forEach(row => {
        let value = col?.valueGetter && typeof col.valueGetter === 'function'
          ? col.valueGetter({ data: row })
          : row?.[col?.field];

        // Handle array values (use valueIndex) - same as ag-grid
        if (Array.isArray(value)) {
          const idx = typeof child.valueIndex === 'number' ? child.valueIndex : 0;
          value = value[idx] ?? 0;
        }

        const numValue = Number(value) || 0;
        total += numValue;
      });

      const columnKey = child.displayName || col?.headerName || col?.field;
      totals[columnKey] = total;
    });

    return totals;
  }

  getTotalRowData(): any[] {
    if (!this.rowData?.length) return [];

    // Get the total row if it exists - same logic as ag-grid
    const totalFields = [
      'userName', 'projectTitle', 'name', 'firstName',
      'source', 'subSource', 'agencyName', 'city',
      'status', 'subStatus',
    ];

    return this.rowData.filter(row =>
      row && totalFields.some(field => row?.[field] === 'Total')
    );
  }

  createExcelDataWithTotals(): { headers: string[][], data: any[], totals: any, flatColumns: any[], valueLabelColIndexes: number[] } {
    const allData = this.rowData || [];
    const calculatedTotals = this.calculateTotalsForSelectedColumns();
    // Always use the full, unfiltered column definitions for Excel export
    const allColumns = this.gridOptions?.columnDefs || [];
    const flatColumns: any[] = [];
    const parentHeaders: string[] = [];
    const childHeaders: string[] = [];
    const valueLabelColIndexes: number[] = [];
    let colIdx = 0;

    // Build parent/child header structure, handling valueLabels
    allColumns.forEach((col: any) => {
      if (col.children && col.children.length > 0) {
        col.children.forEach((child: any) => {
          if (child.valueLabels && Array.isArray(child.valueLabels)) {
            child.valueLabels.forEach((label: string, idx: number) => {
              parentHeaders.push(col.headerName || ''); // Use parent column name as parent header
              childHeaders.push(label);
              flatColumns.push({ ...child, parentName: col.headerName, displayName: label, valueIndex: idx });
              valueLabelColIndexes.push(colIdx++);
            });
          } else {
            parentHeaders.push(col.headerName || '');
            childHeaders.push(child.headerName || '');
            flatColumns.push({ ...child, parentName: col.headerName });
            colIdx++;
          }
        });
      } else if (col.valueLabels && Array.isArray(col.valueLabels)) {
        col.valueLabels.forEach((label: string, idx: number) => {
          parentHeaders.push('');
          childHeaders.push(label);
          flatColumns.push({ ...col, displayName: label, valueIndex: idx });
          valueLabelColIndexes.push(colIdx++);
        });
      } else {
        parentHeaders.push(col.headerName || '');
        childHeaders.push(col.headerName || '');
        flatColumns.push(col);
        colIdx++;
      }
    });

    // Create data rows with ALL columns (no Category column)
    const data: any[] = [];
    allData.forEach(row => {
      const rowData: any = {};
      flatColumns.forEach(col => {
        if (col.field) {
          let value = col?.valueGetter && typeof col.valueGetter === 'function'
            ? col.valueGetter({ data: row })
            : row?.[col?.field];
          if (Array.isArray(value)) {
            const idx = typeof col.valueIndex === 'number' ? col.valueIndex : 0;
            value = value[idx] ?? 0;
          }
          // Use a unique key for valueLabels columns
          const key = (Array.isArray(col.valueLabels) && col.displayName)
            ? `${col.field}_${col.displayName}`
            : col.field;
          rowData[key] = value ?? 0;
        }
      });
      data.push(rowData);
    });
    return { headers: [parentHeaders, childHeaders], data, totals: calculatedTotals, flatColumns, valueLabelColIndexes };
  }

  async exportGraph() {
    this.isExporting = true;
    this.exportStarted.emit();
    this.exportTriggered.emit();

    const canvas = this.getChartCanvas();
    if (!canvas) return;

    const imageData = this.createChartImage(canvas);
    const workbook = this.createWorkbook();
    const worksheet = workbook.addWorksheet('Chart');

    this.setupWorksheetColumns(worksheet);
    let currentRow = this.addHeaderInfo(worksheet, 1);
    currentRow = this.addFilterInfo(worksheet, currentRow);
    currentRow = this.addTitleRow(worksheet, currentRow);

    const imageStartRow = currentRow;
    this.addChartImage(workbook, worksheet, imageData, canvas, imageStartRow);

    const legendCanvas = await this.addLegendImage(workbook, worksheet, imageStartRow, canvas);

    this.addDataTable(worksheet, imageStartRow, legendCanvas, canvas);

    await this.uploadToS3(workbook);
  }

  private getChartCanvas(): HTMLCanvasElement | null {
    const canvas = document.getElementById('activityChart') as HTMLCanvasElement;
    if (!canvas) {
      console.log('No canvas found, aborting export.');
      return null;
    }
    return canvas;
  }

  private createChartImage(canvas: HTMLCanvasElement): string {
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = canvas.width;
    tempCanvas.height = canvas.height;
    const tempCtx = tempCanvas.getContext('2d');
    if (tempCtx) {
      tempCtx.fillStyle = '#FFFFFF';
      tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
      tempCtx.drawImage(canvas, 0, 0);
    }
    return tempCanvas.toDataURL('image/png');
  }

  private createWorkbook(): ExcelJS.Workbook {
    return new ExcelJS.Workbook();
  }

  private setupWorksheetColumns(worksheet: ExcelJS.Worksheet): void {
    worksheet.getColumn(1).width = 22;
    worksheet.getColumn(2).width = 50;
  }

  private addHeaderInfo(worksheet: ExcelJS.Worksheet, startRow: number): number {
    const headerFields = [
      { label: 'Created By:', value: this.userData?.firstName + this.userData?.lastName },
      { label: 'Type:', value: this.headerTitle?.pageTitle?.getValue() },
      { label: 'Created On:', value: getTimeZoneDate(this.currentDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear') },
    ];

    let currentRow = startRow;
    headerFields.forEach(field => {
      this.styleHeaderCell(worksheet, `A${currentRow}`, field.label, true);
      this.styleHeaderCell(worksheet, `B${currentRow}`, field.value, false);
      currentRow++;
    });
    return currentRow;
  }

  private addFilterInfo(worksheet: ExcelJS.Worksheet, startRow: number): number {
    if (!this.payload) return startRow;

    let currentRow = startRow;
    const skipKeys = ['pageNumber', 'pageSize', 'path', 'isNavigatedFromReports', 'ReportPermission', 'ExportPermission'];

    Object.keys(this.payload).forEach(key => {
      if (skipKeys.includes(key)) return;

      const values = this.payload[key];
      const arr = this.formatFilterValue(key, values) || [];
      const label = this.reportFiltersKeyLabel?.[key] ?? key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      const filteredArr = arr.filter(v => v !== undefined && v !== null && v !== '');

      this.styleHeaderCell(worksheet, `A${currentRow}`, `${label}:`, true);
      this.styleHeaderCell(worksheet, `B${currentRow}`, filteredArr.length ? filteredArr.join(', ') : 'N/A', false);
      currentRow++;
    });
    return currentRow;
  }

  private addTitleRow(worksheet: ExcelJS.Worksheet, startRow: number): number {
    worksheet.insertRow(startRow, []);
    worksheet.mergeCells(`A${startRow}:C${startRow}`);
    const titleCell = worksheet.getCell(`A${startRow}`);
    titleCell.value = this.headerTitle?.pageTitle?.getValue();
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
    titleCell.font = { size: 16, bold: true };
    worksheet.getRow(startRow).height = 28;
    return startRow + 1;
  }

  private addChartImage(workbook: ExcelJS.Workbook, worksheet: ExcelJS.Worksheet, imageData: string, canvas: HTMLCanvasElement, startRow: number): void {
    const imageId = workbook.addImage({
      base64: imageData,
      extension: 'png',
    });
    worksheet.addImage(imageId, {
      tl: { col: 0, row: startRow - 1 },
      ext: { width: canvas.width, height: canvas.height },
    });
  }

  private async addLegendImage(workbook: ExcelJS.Workbook, worksheet: ExcelJS.Worksheet, imageStartRow: number, canvas: HTMLCanvasElement): Promise<any> {
    const legendContainer = document.getElementById('chart-legend-container');
    if (!legendContainer) return null;

    await new Promise(res => setTimeout(res, 200));
    const legendCanvas = await (html2canvas as any).default(legendContainer, { useCORS: false, background: null });
    const legendImgData = legendCanvas.toDataURL('image/png');
    const legendId = workbook.addImage({
      base64: legendImgData,
      extension: 'png',
    });

    const legendRow = imageStartRow + Math.ceil(canvas.height / 20) + 1;
    worksheet.addImage(legendId, {
      tl: { col: 0, row: legendRow - 1 },
      ext: { width: legendCanvas.width, height: legendCanvas.height },
    });

    return legendCanvas;
  }

  private addDataTable(worksheet: ExcelJS.Worksheet, imageStartRow: number, legendCanvas: any, canvas: HTMLCanvasElement): void {
    const excelData = this.createExcelDataWithTotals();
    if (excelData.headers[1].length === 0 || excelData.data.length === 0) return;

    const legendHeight = legendCanvas?.height || 0;
    const dataTableStartRow = imageStartRow + Math.ceil(canvas.height / 20) + Math.ceil(legendHeight / 20) + 5;

    this.writeTableHeaders(worksheet, excelData, dataTableStartRow);
    this.writeTableData(worksheet, excelData, dataTableStartRow);
    this.styleTableHeaders(worksheet, dataTableStartRow);
  }

  private writeTableHeaders(worksheet: ExcelJS.Worksheet, excelData: any, startRow: number): void {
    const { headers: [parentHeaders, childHeaders], flatColumns, valueLabelColIndexes } = excelData;
    const parentRowNum = startRow;
    const childRowNum = startRow + 1;

    let colIndex = 0;
    while (colIndex < parentHeaders.length) {
      const parent = parentHeaders[colIndex];
      const child = childHeaders[colIndex];
      const span = this.getColumnSpan(parentHeaders, colIndex);
      const colLetterStart = this.getExcelColumnLetter(colIndex);
      const colLetterEnd = this.getExcelColumnLetter(colIndex + span - 1);

      // If parent and child are the same and this is not a group, leave parent row blank
      if (parent === child && span === 1) {
        // Leave parent row blank
        const parentCell = worksheet.getCell(`${colLetterStart}${parentRowNum}`);
        parentCell.value = '';
        // Write child header
        const childCell = worksheet.getCell(`${colLetterStart}${childRowNum}`);
        childCell.value = child;
        childCell.alignment = { horizontal: 'center', vertical: 'middle' };
        childCell.font = { bold: true };
        colIndex += span;
        continue;
      }

      // Otherwise, use the normal logic
      this.writeParentHeader(worksheet, parent, child, colLetterStart, colLetterEnd, parentRowNum, childRowNum, span);
      this.setColumnWidth(worksheet, colIndex, span, parentHeaders, childHeaders);
      colIndex += span;
    }

    // Always write the child headers row
    for (let i = 0; i < childHeaders.length; i++) {
      const colLetter = this.getExcelColumnLetter(i);
      const cell = worksheet.getCell(`${colLetter}${childRowNum}`);
      if (!cell.value) { // Only write if not already written above
        cell.value = childHeaders[i];
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
        cell.font = { bold: true };
      }
    }
  }

  private writeTableData(worksheet: ExcelJS.Worksheet, excelData: any, startRow: number): void {
    const { data, flatColumns } = excelData;
    const childRowNum = startRow + 1;

    data.forEach((row: any, rowIndex: number) => {
      const dataRow = childRowNum + 1 + rowIndex;
      let dataColIndex = 0;

      flatColumns.forEach((col: any) => {
        const colLetter = this.getExcelColumnLetter(dataColIndex);
        const cell = worksheet.getCell(`${colLetter}${dataRow}`);
        const key = this.getDataKey(col);
        const value = this.getCellValue(row, key, col);

        cell.value = value;
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
        cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };

        if (rowIndex === data.length - 1 && row[key] === 'Total') {
          cell.font = { bold: true };
          cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF0F0F0' } };
        }
        dataColIndex++;
      });
    });

    this.setDynamicColumnWidths(worksheet, flatColumns, data);
  }

  private async uploadToS3(workbook: ExcelJS.Workbook): Promise<void> {
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const fileName = `${this.reportType}_chart_${getTimeZoneDate(new Date(), this.userData?.timeZoneInfo?.baseUTcOffset, 'fullDateTime')}.xlsx`;
    const file = new File([blob], fileName, { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

    this.s3UploadService.uploadDoc(file, FolderNamesS3.Reports)
      .pipe(takeUntil(this.stopper))
      .subscribe({
        next: (response: any) => {
          if (response.data?.length) {
            const s3BucketKey = response.data[0];
            this.showExportModal(s3BucketKey);
          }
          this.exportFinished.emit();
          this.isExporting = false;
        },
      });
  }

  private styleHeaderCell(worksheet: ExcelJS.Worksheet, cellAddress: string, value: any, isLabel: boolean): void {
    const cell = worksheet.getCell(cellAddress);
    cell.value = value;
    cell.font = { bold: isLabel };
    cell.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFD3D3D3' } };
    cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
  }

  private getColumnSpan(parentHeaders: string[], startIndex: number): number {
    const parent = parentHeaders[startIndex];
    let span = 1;
    for (let j = startIndex + 1; j < parentHeaders.length; j++) {
      if (parentHeaders[j] === parent) span++;
      else break;
    }
    return span;
  }

  private writeParentHeader(worksheet: ExcelJS.Worksheet, parent: string, child: string, colLetterStart: string, colLetterEnd: string, parentRowNum: number, childRowNum: number, span: number): void {
    if (span > 1) {
      worksheet.mergeCells(`${colLetterStart}${parentRowNum}:${colLetterEnd}${parentRowNum}`);
      const cell = worksheet.getCell(`${colLetterStart}${parentRowNum}`);
      cell.value = parent;
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.font = { bold: true };
    } else if (parent === child) {
      // Do NOT merge, just write both parent and child headers
      const parentCell = worksheet.getCell(`${colLetterStart}${parentRowNum}`);
      parentCell.value = parent;
      parentCell.alignment = { horizontal: 'center', vertical: 'middle' };
      parentCell.font = { bold: true };

      const childCell = worksheet.getCell(`${colLetterStart}${childRowNum}`);
      childCell.value = child;
      childCell.alignment = { horizontal: 'center', vertical: 'middle' };
      childCell.font = { bold: true };
    } else {
      const cell = worksheet.getCell(`${colLetterStart}${parentRowNum}`);
      cell.value = parent;
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.font = { bold: true };
    }
  }

  private writeChildHeaders(worksheet: ExcelJS.Worksheet, parentHeaders: string[], childHeaders: string[], valueLabelColIndexes: number[], childRowNum: number): void {
    for (let i = 0; i < childHeaders.length; i++) {
      const colLetter = this.getExcelColumnLetter(i);
      // Always write the child header
      const cell = worksheet.getCell(`${colLetter}${childRowNum}`);
      cell.value = childHeaders[i];
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.font = { bold: true };
    }
  }

  private setColumnWidth(worksheet: ExcelJS.Worksheet, colIndex: number, span: number, parentHeaders: string[], childHeaders: string[]): void {
    let maxLen = parentHeaders[colIndex] ? parentHeaders[colIndex].length : 0;
    for (let k = 0; k < span; k++) {
      maxLen = Math.max(maxLen, (childHeaders[colIndex + k] || '').length);
    }
    worksheet.getColumn(colIndex + 1).width = Math.max(15, maxLen + 5);
  }

  private setDynamicColumnWidths(worksheet: ExcelJS.Worksheet, flatColumns: any[], data: any[]): void {
    for (let i = 0; i < flatColumns.length; i++) {
      let maxLen = 0;
      const headerText = flatColumns[i].displayName || flatColumns[i].headerName || '';
      maxLen = Math.max(maxLen, headerText.length);

      data.forEach(row => {
        const key = this.getDataKey(flatColumns[i]);
        const val = row[key];
        if (val !== undefined && val !== null) {
          maxLen = Math.max(maxLen, String(val).length);
        }
      });

      worksheet.getColumn(i + 1).width = Math.max(15, maxLen + 2);
    }
  }

  private styleTableHeaders(worksheet: ExcelJS.Worksheet, startRow: number): void {
    [startRow, startRow + 1].forEach(rowNum => {
      worksheet.getRow(rowNum).eachCell(cell => {
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF000000' } };
        cell.font = { color: { argb: 'FFFFFFFF' }, bold: true };
      });
    });
  }

  private getDataKey(col: any): string {
    return (Array.isArray(col.valueLabels) && col.displayName) ? `${col.field}_${col.displayName}` : col.field;
  }

  private getCellValue(row: any, key: string, col: any): any {
    const isManagerColumn = (col.headerName === 'General Manager' || col.headerName === 'Reporting Manager');
    const isTotalRow = (row[key] === 'Total');

    if (isManagerColumn && (row[key] === 0 || row[key] === '0') && !isTotalRow) {
      return '';
    }
    return (row[key] !== undefined && row[key] !== null && row[key] !== '') ? row[key] : '';
  }

  private showExportModal(s3BucketKey: string): void {
    const initialState: any = {
      payload: {
        ...this.payload,
        timeZoneId: this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
        s3BucketKey: this.s3BucketUrl + s3BucketKey,
        ...this.getReportTypeConfig()
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };

    this.modalService.show(ExportMailComponent, {
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
      initialState
    });
  }

  private getReportTypeConfig(): any {
    switch (this.reportType) {
      case 'combined-activity-report':
        return { shouldShowDataReport: true, path: 'report/activity/level9' };
      case 'activity-report':
        return { shouldShowDataReport: false, path: 'report/activity/level9' };
      case 'data-activity-report':
        return { shouldShowDataReport: false, path: 'datareport/activity' };
      default:
        return {};
    }
  }

  getUserDisplayName(userId: string) {
    let userName = '';
    this.allUsers?.forEach((user: any) => {
      if (userId === user.id) userName = `${user.fullName}`;
    });
    return userName;
  }

  resetChartSelections() {
    this.selectedChartType = this.chartTypes[0].value;
    this.graphStateService.updateChartType(this.reportType, this.selectedChartType);
    this.initializeDefaultSelections();
    this.updateChartWithDelay();
  }
}