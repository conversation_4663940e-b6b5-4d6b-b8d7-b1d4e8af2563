import { Compo<PERSON>, EventE<PERSON>ter, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { UpdateGlobalSettings } from 'src/app/reducers/global-settings/global-settings.actions';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'property-settings',
  templateUrl: './property-settings.component.html',
})
export class PropertySettingsComponent implements OnI<PERSON>t, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  canView: boolean;
  canUpdate: boolean;
  propertySettingsForm: FormGroup;
  message: string;
  notes: string;
  settingProperty: any;
  isProjectSettings: boolean;
  isMicrositeOpen: boolean = false;

  constructor(
    private headerTitle: HeaderTitleService,
    private _store: Store<AppState>,
    private fb: FormBuilder,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    public metaTitle: Title,
    private router: Router,
  ) { }

  ngOnInit(): void {
    this.propertySettingsForm = this.fb.group({
      exportProperty: [null],
      exportProject: [null],
      shouldEnableEnquiryForm: [null],
      showMoreMicrositeProperties: [null]
    });

    this.metaTitle.setTitle('CRM | Global Config');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.GlobalSettings.View'))
          this.canView = true;
        if (permissions?.includes('Permissions.GlobalSettings.Update'))
          this.canUpdate = true;
      });

    this.patchValues();
  }

  patchValues() {
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.settingProperty = data;
        this.propertySettingsForm.patchValue({
          exportProperty: data.isPropertiesExportEnabled,
          exportProject: data.isProjectsExportEnabled,
          shouldEnableEnquiryForm: data.shouldEnableEnquiryForm,
          showMoreMicrositeProperties: data?.showMoreMicrositeProperties
        });
      });
  }

  openConfirmModal(changePopup: any, settingType: string) {
    this.modalRef = this.modalService.show(changePopup, {
      class: 'modal-600 top-modal ip-modal-unset',
      ignoreBackdropClick: true,
      keyboard: false,
    });
    switch (settingType) {
      case 'exportProperty':
        if (this.propertySettingsForm?.value?.exportProperty) {
          this.message =
            'Are you sure you want to disable the “Export” option?';
          this.notes =
            'Users without export permission will not be able to export property anymore.';
        } else {
          this.message = 'Are you sure you want to enable the “Export” option?';
          this.notes =
            'Users with export permission will be able to export property.';
        }
        break;
      case 'exportProject':
        if (this.propertySettingsForm?.value?.exportProject) {
          this.message =
            'Are you sure you want to disable the “Export” option?';
          this.notes =
            'Users without export permission will not be able to export project anymore.';
        } else {
          this.message = 'Are you sure you want to enable the “Export” option?';
          this.notes =
            'Users with export permission will be able to export project.';
        }
        break;
      case 'shouldEnableEnquiryForm':
        if (this.propertySettingsForm?.value?.shouldEnableEnquiryForm) {
          this.message =
            'Are you sure you want to disable the “Enquiry Form” option?';
          this.notes =
            'Users will not be able to see the enquiry form.';
        } else {
          this.message = 'Are you sure you want to enable the “Enquiry Form” option?';
          this.notes =
            'Users will be able to see the enquiry form.';
        }
        break;
        case 'showMoreMicrositeProperties':
          if (this.propertySettingsForm?.value?.showMoreMicrositeProperties) {
            this.message =
              'Are you sure you want to disable the “Show More Properties” option?';
            this.notes =
              'Users will not be able to see the show more properties.';
          } else {
            this.message = 'Are you sure you want to enable the " Show More Properties" Option?';
            this.notes =
              'Users will be able to see the show more properties.';
          }
          break;
    }
  }

  onSave() {
    const settingsProperty: any = this.propertySettingsForm?.value;
    let payload: any = { ...this.settingProperty };
    payload.isProjectsExportEnabled = settingsProperty.exportProject;
    payload.isPropertiesExportEnabled = settingsProperty.exportProperty;
    payload.shouldEnableEnquiryForm = settingsProperty.shouldEnableEnquiryForm
    payload.showMoreMicrositeProperties = settingsProperty.showMoreMicrositeProperties
    this._store.dispatch(new UpdateGlobalSettings(payload));
    this.modalRef.hide();
  }

  closePopup() {
    this.patchValues();
    this.modalRef.hide();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}