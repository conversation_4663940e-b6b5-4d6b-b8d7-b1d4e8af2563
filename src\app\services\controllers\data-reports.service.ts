import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';
@Injectable({
    providedIn: 'root',
})
export class DataReportsService extends BaseService<any> {
    public page: number;
    public count: number;
    serviceBaseUrl: string;
    exporSubSourceDataStatus: any;
    constructor(private http: HttpClient) {
        super(http);
        this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
    }

    getResourceUrl(): string {
        return 'datareport';
    }

    exportUserDataStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/user/status/export`, payload);
    }

    exportProjectDataStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/project/status/export`, payload);
    }

    exportSourceDataStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/source/status/export`, payload);
    }

    exportSubSourceDataStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/subsource/status/export`, payload);
    }

    exportCallData(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/user/call-log/export`, payload);
    }
    
    exportDataActivity(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/activity/export/email`, payload);
    }

    getDataReportsAgencyExport(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/agency/status/export`, payload);
    }
}