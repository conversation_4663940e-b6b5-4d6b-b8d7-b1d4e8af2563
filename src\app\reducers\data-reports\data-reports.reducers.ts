import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { DataReportsActionTypes, FetchDataActivityExportSuccess, FetchDataAgencyExportSuccess, FetchDataCallExportSuccess, FetchDataProjectExportSuccess, FetchDataReportsActivityCommunicationSuccess, FetchDataReportsActivitySuccess, FetchDataReportsActivityTotalCountSuccess, FetchDataReportsAgencySuccess, FetchDataReportsAgencyTotalCountSuccess, FetchDataReportsCallSuccess, FetchDataReportsCallTotalCountSuccess, FetchDataReportsProjectSuccess, FetchDataReportsProjectTotalCountSuccess, FetchDataReportsSourceSuccess, FetchDataReportsSourceTotalCountSuccess, FetchDataReportsSubSourceSuccess, FetchDataReportsSubSourceTotalCountSuccess, FetchDataReportsUserSuccess, FetchDataReportsUserTotalCountSuccess, FetchDataSourceExportSuccess, FetchDataSubSourceExportSuccess, FetchDataUserExportSuccess, UpdateDataActivityFilterPayload, UpdateDataAgencyFilterPayload, UpdateDataCallFilterPayload, UpdateDataProjectFilterPayload, UpdateDataSourceFilterPayload, UpdateDataSubSourceFilterPayload, UpdateDataUserFilterPayload } from './data-reports.action';
export type DataReportsState = {
    dataActivityTotalCount: number;
    subsources: any;
    isSubsourcesLoading: boolean;
    dataSubSourcesTotalCount: any;
    dataSubSourceExport: any;
    dataSubSourceFiltersPayload: any;
    dataSourceExport: any;
    dataSourcesTotalCount: number;
    sources: any;
    isSourcesLoading: boolean;
    dataSourceFiltersPayload: any;
    dataProjectExport: any;
    dataProjectFiltersPayload: any;
    projects: any;
    isProjectsLoading: boolean;
    dataProjectsTotalCount: any;
    dataUserExport: any;
    dataUsersTotalCount: number;
    users?: any;
    isUsersLoading: boolean;
    dataUserFiltersPayload: any;
    call: any;
    dataCallTotalCount: any;
    dataCallExport: any;
    dataCallFiltersPayload: any;
    isDataCallReportLoading: boolean;
    activityFiltersPayload: any;
    activityExport: any;
    dataActivityFiltersPayload: any;
    dataActivityExport: any;
    activity: any;
    activitycommunication: any;
    activityIsLoading: boolean;
    activitycommunicationIsLoading: boolean;
    agencies: any;
    isAgenciesLoading: boolean;
    dataAgenciesTotalCount: any;
    dataAgencyExport: any;
    dataAgencyFiltersPayload: any;
};
const initialState: DataReportsState = {
    users: [],
    isUsersLoading: true,
    dataUserFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'datareport/user/status',
    },
    dataUsersTotalCount: 0,
    dataUserExport: {},
    dataProjectExport: {},
    dataProjectFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'datareport/project/status',
    },
    projects: [],
    isProjectsLoading: true,
    dataProjectsTotalCount: 0,
    dataSourceExport: {},
    dataSourcesTotalCount: 0,
    sources: [],
    isSourcesLoading: true,
    activitycommunication: [],
    activity: [],
    dataSourceFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'datareport/source/status',
    },
    subsources: [],
    isSubsourcesLoading: true,
    dataSubSourcesTotalCount: 0,
    dataSubSourceExport: {},
    dataSubSourceFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'datareport/subsource/status',
    },
    call: [],
    dataCallTotalCount: 0,
    dataCallExport: {},
    dataCallFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'datareport/user/data-call-log',
    },
    isDataCallReportLoading: true,
    activityExport: {},
    activityFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'datareport/activity/communication',
        // fromDate: new Date(),
        // toDate: new Date(),
    },
    activityIsLoading: true,
    activitycommunicationIsLoading: true,
    dataActivityTotalCount: 0,
    dataActivityExport: {},
    dataActivityFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'datareport/activity/communication',
        // fromDate: new Date(),
        // toDate: new Date(),
    },
    agencies: [],
    isAgenciesLoading: true,
    dataAgenciesTotalCount: 0,
    dataAgencyExport: {},
    dataAgencyFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'datareport/agency/status',
    },
};
export function dataReportsReducer(
    state: DataReportsState = initialState,
    action: Action
): DataReportsState {
    switch (action.type) {
        case DataReportsActionTypes.FETCH_DATA_REPORTS_USER:
            return {
                ...state,
                users: [],
                isUsersLoading: true,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_USER_SUCCESS:
            return {
                ...state,
                users: (action as FetchDataReportsUserSuccess).response.items,
                isUsersLoading: false,
            };
        case DataReportsActionTypes.UPDATE_DATA_USER_FILTER_PAYLOAD:
            return {
                ...state,
                dataUserFiltersPayload: (action as UpdateDataUserFilterPayload).filter,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_USER_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                dataUsersTotalCount: (action as FetchDataReportsUserTotalCountSuccess).response,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_USER_EXPORT_SUCCESS:
            return {
                ...state,
                dataUserExport: (action as FetchDataUserExportSuccess).response,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_PROJECT:
            return {
                ...state,
                projects: [],
                isProjectsLoading: true,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_PROJECT_SUCCESS:
            return {
                ...state,
                projects: (action as FetchDataReportsProjectSuccess).response.items,
                isProjectsLoading: false,
            };
        case DataReportsActionTypes.UPDATE_DATA_PROJECT_FILTER_PAYLOAD:
            return {
                ...state,
                dataProjectFiltersPayload: (action as UpdateDataProjectFilterPayload).filter,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_PROJECT_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                dataProjectsTotalCount: (action as FetchDataReportsProjectTotalCountSuccess).response,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_PROJECT_EXPORT_SUCCESS:
            return {
                ...state,
                dataProjectExport: (action as FetchDataProjectExportSuccess).response,
            };
        //source
        case DataReportsActionTypes.FETCH_DATA_REPORTS_SOURCE:
            return {
                ...state,
                isSourcesLoading: true,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_SOURCE_SUCCESS:
            return {
                ...state,
                sources: (action as FetchDataReportsSourceSuccess).response.items,
                isSourcesLoading: false,
            };
        case DataReportsActionTypes.UPDATE_DATA_SOURCE_FILTER_PAYLOAD:
            return {
                ...state,
                dataSourceFiltersPayload: (action as UpdateDataSourceFilterPayload).filter,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_SOURCE_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                dataSourcesTotalCount: (action as FetchDataReportsSourceTotalCountSuccess).response,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_SOURCE_EXPORT_SUCCESS:
            return {
                ...state,
                dataSourceExport: (action as FetchDataSourceExportSuccess).response,
            };
        //SUBSOURCE
        case DataReportsActionTypes.FETCH_DATA_REPORTS_SUB_SOURCE:
            return {
                ...state,
                isSubsourcesLoading: true,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_SUB_SOURCE_SUCCESS:
            return {
                ...state,
                subsources: (action as FetchDataReportsSubSourceSuccess).response.items,
                isSubsourcesLoading: false,
            };
        case DataReportsActionTypes.UPDATE_DATA_SUB_SOURCE_FILTER_PAYLOAD:
            return {
                ...state,
                dataSubSourceFiltersPayload: (action as UpdateDataSubSourceFilterPayload).filter,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_SUB_SOURCE_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                dataSubSourcesTotalCount: (action as FetchDataReportsSubSourceTotalCountSuccess).response,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_SUB_SOURCE_EXPORT_SUCCESS:
            return {
                ...state,
                dataSubSourceExport: (action as FetchDataSubSourceExportSuccess).response,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_CALL_EXPORT_SUCCESS:
            return {
                ...state,
                dataCallExport: (action as FetchDataCallExportSuccess).response,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_CALL:
            return {
                ...state,
                isDataCallReportLoading: true
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_CALL_SUCCESS:
            return {
                ...state,
                call: (action as FetchDataReportsCallSuccess).response.items,
                isDataCallReportLoading: false
            };
        case DataReportsActionTypes.UPDATE_DATA_CALL_FILTER_PAYLOAD:
            return {
                ...state,
                dataCallFiltersPayload: (action as UpdateDataCallFilterPayload).filter,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_CALL_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                dataCallTotalCount: (action as FetchDataReportsCallTotalCountSuccess).response,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_CALL_EXPORT_SUCCESS:
            return {
                ...state,
                dataCallExport: (action as FetchDataCallExportSuccess).response,
            };
        case DataReportsActionTypes.FETCH_DATA_ACTIVITY_REPORTS:
            return {
                ...state,
                activityIsLoading: true,
            };
        case DataReportsActionTypes.FETCH_DATA_ACTIVITY_REPORTS_SUCCESS:
            return {
                ...state,
                activity: (action as FetchDataReportsActivitySuccess).response?.items,
                activityIsLoading: false,
            };
        case DataReportsActionTypes.FETCH_DATA_ACTIVITY_COMMUNICATION_REPORTS:
            return {
                ...state,
                activitycommunicationIsLoading: true,
            };
        case DataReportsActionTypes.FETCH_DATA_ACTIVITY_COMMUNICATION_REPORTS_SUCCESS:
            return {
                ...state,
                activitycommunication: (action as FetchDataReportsActivityCommunicationSuccess).response.items,
                activitycommunicationIsLoading: false,
            };
        case DataReportsActionTypes.FETCH_DATA_ACTIVITY_REPORTS_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                dataActivityTotalCount: (action as FetchDataReportsActivityTotalCountSuccess)
                    .response,
            };

        case DataReportsActionTypes.UPDATE_DATA_ACTIVITY_FILTER_PAYLOAD:
            return {
                ...state,
                dataActivityFiltersPayload: (action as UpdateDataActivityFilterPayload).filter,
            };
        case DataReportsActionTypes.FETCH_DATA_ACTIVITY_REPORTS_EXPORT_SUCCESS:
            return {
                ...state,
                dataActivityExport: (action as FetchDataActivityExportSuccess).response,
            };
        //agency
        case DataReportsActionTypes.FETCH_DATA_REPORTS_AGENCY:
            return {
                ...state,
                agencies: [],
                isAgenciesLoading: true,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_AGENCY_SUCCESS:
            return {
                ...state,
                agencies: (action as FetchDataReportsAgencySuccess).response.items,
                isAgenciesLoading: false,
            };
        case DataReportsActionTypes.UPDATE_DATA_AGENCY_FILTER_PAYLOAD:
            return {
                ...state,
                dataAgencyFiltersPayload: (action as UpdateDataAgencyFilterPayload).filter,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_AGENCY_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                dataAgenciesTotalCount: (action as FetchDataReportsAgencyTotalCountSuccess).response,
            };
        case DataReportsActionTypes.FETCH_DATA_REPORTS_AGENCY_EXPORT_SUCCESS:
            return {
                ...state,
                dataAgencyExport: (action as FetchDataAgencyExportSuccess).response,
            };
        default:
            return state;
    }
}
export const selectFeature = (state: AppState) => state.dataReports;

export const getDataUserFiltersPayload = createSelector(
    selectFeature,
    (state: DataReportsState) => state.dataUserFiltersPayload
);

export const getDataReportsUsersList = createSelector(
    selectFeature,
    (state: DataReportsState) => {
        return {
            items: state.users,
            totalCount: state.dataUsersTotalCount,
            isLoading: state?.isUsersLoading
        };
    }
);

export const getUserExport = createSelector(
    selectFeature,
    (state: DataReportsState) => state.dataUserExport
);
//project
export const getDataProjectFiltersPayload = createSelector(
    selectFeature,
    (state: DataReportsState) => state.dataProjectFiltersPayload
);
export const getDataReportsProjectsList = createSelector(
    selectFeature,
    (state: DataReportsState) => {
        return {
            items: state.projects,
            totalCount: state.dataProjectsTotalCount,
            isLoading: state.isProjectsLoading
        };
    }
);

export const getProjectExport = createSelector(
    selectFeature,
    (state: DataReportsState) => state.dataProjectExport
);
//source
export const getDataSourceFiltersPayload = createSelector(
    selectFeature,
    (state: DataReportsState) => state.dataSourceFiltersPayload
);
export const getDataReportsSourcesList = createSelector(
    selectFeature,
    (state: DataReportsState) => {
        return {
            items: state.sources,
            totalCount: state.dataSourcesTotalCount,
            isLoading: state?.isSourcesLoading
        };
    }
);

export const getSourceExport = createSelector(
    selectFeature,
    (state: DataReportsState) => state.dataSourceExport
);

//SUBSOURCE
export const getDataSubSourceFiltersPayload = createSelector(
    selectFeature,
    (state: DataReportsState) => state.dataSubSourceFiltersPayload
);
export const getDataReportsSubSourcesList = createSelector(
    selectFeature,
    (state: DataReportsState) => {
        return {
            items: state.subsources,
            totalCount: state.dataSubSourcesTotalCount,
            isLoading: state?.isSubsourcesLoading
        };
    }
);

export const getSubSourceExport = createSelector(
    selectFeature,
    (state: DataReportsState) => state.dataSubSourceExport
);


//call

export const getDataCallFiltersPayload = createSelector(
    selectFeature,
    (state: DataReportsState) => state.dataCallFiltersPayload
);
export const getDataReportsCallList = createSelector(
    selectFeature,
    (state: DataReportsState) => {
        return {
            items: state.call,
            totalCount: state.dataCallTotalCount,
        };
    }
);

export const getReportsDataCallListIsLoading = createSelector(
    selectFeature,
    (state: DataReportsState) => {
        return state?.isDataCallReportLoading;
    }
);

export const getCallExport = createSelector(
    selectFeature,
    (state: DataReportsState) => state.dataCallExport
);

//activity

export const getDataActivityFiltersPayload = createSelector(
    selectFeature,
    (state: DataReportsState) => state.dataActivityFiltersPayload
);

export const getDataReportsActivityList = createSelector(
    selectFeature,
    (state: DataReportsState) => {
        return {
            items: state.activity,
            totalCount: state.dataActivityTotalCount,
        };
    }
);

export const getReportsActivityCommunicationList = createSelector(
    selectFeature,
    (state: DataReportsState) => {
        return {
            items2: state.activitycommunication,
            isLoading: state.activitycommunicationIsLoading,
        };
    }
);
export const getActivityIsLoading = createSelector(
    selectFeature,
    (state: DataReportsState) => state.activityIsLoading
);

export const getActivityCommunicationIsLoading = createSelector(
    selectFeature,
    (state: DataReportsState) => state.activitycommunicationIsLoading
);

//agency
export const getDataAgencyFiltersPayload = createSelector(
    selectFeature,
    (state: DataReportsState) => state.dataAgencyFiltersPayload
);

export const getDataReportsAgenciesList = createSelector(
    selectFeature,
    (state: DataReportsState) => {
        return {
            items: state.agencies,
            totalCount: state.dataAgenciesTotalCount,
            isLoading: state.isAgenciesLoading,
        };
    }
);

export const getAgencyExport = createSelector(
    selectFeature,
    (state: DataReportsState) => state.dataAgencyExport
);
