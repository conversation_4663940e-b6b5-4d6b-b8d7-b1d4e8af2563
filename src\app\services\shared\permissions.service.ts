import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

interface RoutePermissionMapping {
  url: string;
  permissions?: string[];
}

@Injectable({
  providedIn: 'root',
})
export class PermissionsService {
  userDetails: any;
  userId: string;
  constructor(private router: Router) { }

  routeToUrl(url: string, distinctPermissions: string[]): void {
    const distinctPermissionsString = distinctPermissions.toString();
    this.userDetails = localStorage.getItem('userDetails');
    this.userId = JSON.parse(this.userDetails)?.sub;
    const routePermissionMappings: RoutePermissionMapping[] = [
      {
        url: '/dashboard',
        permissions: [
          'Permissions.Dashboard.ViewOrg',
          'Permissions.Dashboard.ViewTeam',
          'Permissions.Dashboard.View',
        ],
      },
      {
        url: '/global-config',
        permissions: [
          'Permissions.Integration.View',
          'Permissions.Integration.Create',
          'Permissions.Integration.Update',
        ],
      },
      {
        url: '/global-config/integration',
        permissions: [
          'Permissions.Integration.View',
          'Permissions.Integration.Create',
          'Permissions.Integration.Update',
        ],
      },
      {
        url: '/global-config/integrations',
        permissions: [
          'Permissions.Integration.View',
          'Permissions.Integration.Create',
          'Permissions.Integration.Update',
        ],
      },
      {
        url: '/global-config/manage-source',
        permissions: [
          'Permissions.GlobalSettings.View',
          'Permissions.GlobalSettings.Hide',
          'Permissions.GlobalSettings.Unhide',
        ],
      },
      {
        url: '/leads/manage-leads',
        permissions: ['Permissions.Leads.View'],
      },
      {
        url: '/leads/add-lead',
        permissions: ['Permissions.Leads.Create'],
      },
      {
        url: '/leads/bulk-upload',
        permissions: ['Permissions.Leads.BulkUpload'],
      },
      {
        url: '/external/add-lead/qr-code',
        permissions: [''],
      },
      {
        url: '/external/property-preview',
        permissions: [''],
      },
      {
        url: '/external/listing-preview',
        permissions: [''],
      },
      {
        url: '/leads/edit-lead',
        permissions: ['Permissions.Leads.Update'],
      },
      {
        url: '/data/manage-data',
        permissions: ['Permissions.Prospects.View'],
      },
      {
        url: '/data/add-data',
        permissions: ['Permissions.Prospects.Create'],
      },
      {
        url: '/data/edit-data',
        permissions: ['Permissions.Prospects.Update'],
      },
      {
        url: '/data/bulk-upload',
        permissions: ['Permissions.Prospects.BulkUpload'],
      },
      {
        url: '/data/data-preview',
        permissions: ['Permissions.Prospects.Create'],
      },
      {
        url: '/external/project-preview',
        permissions: [''],
      },
      {
        url: '/this-applications',
        permissions: [''],
      },
      {
        url: '/projects/manage-projects',
        permissions: ['Permissions.Projects.View'],
      },
      {
        url: '/projects/add-project',
        permissions: ['Permissions.Projects.View',
          'Permissions.Projects.Create'],
      },
      {
        url: '/projects/edit-project',
        permissions: ['Permissions.Projects.View',
          'Permissions.Projects.Update'],
      },
      {
        url: '/properties/manage-properties',
        permissions: ['Permissions.Properties.View'],
      },
      {
        url: '/properties/manage-listing',
        permissions: ['Permissions.Properties.View'],
      },
      {
        url: '/properties/add-property',
        permissions: ['Permissions.Properties.Create'],
      },
      {
        url: '/properties/edit-property',
        permissions: ['Permissions.Properties.Update'],
      },
      {
        url: '/properties/bulk-upload',
        permissions: ['Permissions.Properties.BulkUpload'],
      },
      {
        url: '/teams/user-details',
        permissions: ['Permissions.Users.View'],
      },
      {
        url: '/teams/manage-team',
        permissions: ['Permissions.Teams.View'],
      },
      {
        url: '/teams/manage-member',
        permissions: ['Permissions.Teams.View'],
      },
      {
        url: '/teams/bulk-upload',
        permissions: ['Permissions.Users.Create'],
      },
      {
        url: '/reports/status-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/data-status-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/sub-status-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/activity-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/data-activity-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/combined-activity-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/merge-activity-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/project-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/data-project-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/source-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/data-source-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/sub-source-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },

      {
        url: '/reports/data-subsource-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/agency-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/visit-meeting-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/received-date-source',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/substatus-subsource',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/project-substatus',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/call-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/data-call-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/user-source-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/user-subsource-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/history-visit-meeting-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/merge-meeting-visit-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/city-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/reports/data-agency-report',
        permissions: [
          'Permissions.Reports.ViewAllUsers',
          'Permissions.Reports.ViewReportees',
        ],
      },
      {
        url: '/attendance',
        permissions: [
          'Permissions.Attendance.ViewAllUsers',
          'Permissions.Attendance.ViewReportees',
        ],
      },
      {
        url: '/task/manage-task',
        permissions: ['Permissions.Todos.View'],
      },
      {
        url: '/teams/manage-user',
        permissions: ['Permissions.Users.View'],
      },
      {
        url: '/teams/add-user',
        permissions: ['Permissions.Users.Create'],
      },
      {
        url: '/teams/edit-user',
        permissions: ['Permissions.Users.Update'],
      },
      {
        url: '/teams/manage-role',
        permissions: ['Permissions.Roles.View'],
      },
      {
        url: '/profile/profile-dashboard',
        permissions: [
          'Permissions.OrgProfile.View',
          'Permissions.OrgProfile.Update',
        ],
      },
      {
        // url: '/user-profile/user-dashboard',
        url: `/teams/user-details/${this.userId}`,
      },
      {
        url: '/invoice',
        permissions: ['Permissions.Invoice.View'],
      },
      {
        url: '/engage-to',
        permissions: ['Permissions.GlobalSettings.View'],
      },
      {
        url: '/whatsApp-inbox',
        permissions: ['Permissions.Leads.View'],
      },
      {
        url: '/listing-management',
        permissions: ['Permissions.Properties.View']
      },
      {
        url: '/manage-reference-id',
        permissions: ['Permissions.Properties.View']
      },
      {
        url: '/reference-bulk-upload',
        permissions: ['Permissions.Properties.View']
      },
      {
        url: '/properties/add-listing',
        permissions: ['Permissions.Properties.Create']
      },
      {
        url: '/properties/edit-listing',
        permissions: ['Permissions.Properties.Update']
      },
      {
        url: '/properties/listing-bulk-upload',
        permissions: ['Permissions.Properties.Update']
      },
      {
        url: '/properties/bulk-address',
        permissions: ['Permissions.Integration.Create']
      },
      {
        url: '/projects/project-bulk-upload',
        permissions: ['Permissions.Projects.BulkUpload']
      },
    ];

    const matchedMapping: RoutePermissionMapping = routePermissionMappings.find(
      (mapping: RoutePermissionMapping) =>
        url.includes(mapping.url) &&
        (!mapping.permissions ||
          mapping.permissions.some((permission: string) =>
            distinctPermissionsString.includes(permission)
          ))
    );
    const routeToNavigate: string = matchedMapping
      ? url
      : `/teams/user-details/${this.userId}`;
    this.router.navigateByUrl(routeToNavigate);
  }
}
