<ng-container *ngIf="canView">
    <div class="pt-12 px-30 position-relative">
        <div class="flex-between">
            <div class="pt-12 align-center">
                <div class="icon ic-chevron-left ic-xxs ic-coal cursor-pointer mr-16" routerLink='/global-config'></div>
                <span class="icon ic-house-solid ic-sm ic-black mr-8"></span>
                <h5 class="fw-600">Projects and Properties {{ 'GLOBAL.settings' | translate }}
                </h5>
            </div>
        </div>
        <form [formGroup]="propertySettingsForm">
            <div class="bg-white pl-20 py-16 mt-20 flex-between br-6">
                <div>
                    <h5 class="fw-600">{{ 'REPORTS.export' | translate }} Property</h5>
                    <h6 class="text-dark-gray pt-4">{{ 'SETTINGS.export-description'| translate }}</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{propertySettingsForm.get('exportProperty').value == true ? 'on' :
                        'off'}}</div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup,'exportProperty') : ''"
                        formControlName='exportProperty' id="chkexportProperty" name="exportProperty"
                        [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkexportProperty" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>

            </div>
            <div class="bg-white pl-20 py-16 mt-20 flex-between br-6">
                <div>
                    <h5 class="fw-600">{{ 'REPORTS.export' | translate }} Project</h5>
                    <h6 class="text-dark-gray pt-4">{{ 'SETTINGS.export-description'| translate }}</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{propertySettingsForm.get('exportProject').value == true ? 'on' : 'off'}}
                    </div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup,'exportProject') : ''"
                        formControlName='exportProject' id="chkexportProject" name="exportProject"
                        [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkexportProject" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>

            </div>
            <div class="bg-white pl-20 py-16 mt-20 flex-between br-6">
                <div>
                    <h5 class="fw-600">Amenities and Attributes</h5>
                    <h6 class="text-dark-gray pt-4">you can add or modify amenities and attributes</h6>
                </div>
                <div class="align-center mr-40 ph-mr-20" routerLink='/global-config/amenities-attributes'>
                    <h6 class="cursor-pointer text-black-200 fw-400">Click to manage<span
                            class="ml-20 rotate-90 icon ic-black ic-triangle-up ic-xxxs"></span></h6>
                </div>

            </div>
            <div class="mt-12 bg-white mb-12 br-6">
                <div class="flex-between py-16 pl-20 cursor-pointer" (click)="isMicrositeOpen = !isMicrositeOpen;"
                    [ngClass]="{'border-bottom' : isMicrositeOpen}">
                    <div>
                        <h5 class="fw-600">Manage Microsite
                            <span *ngIf="isMicrositeOpen && false"
                                class="dot dot-sm text-white bg-coal br-50 ml-6 text-sm">?</span>
                        </h5>
                        <h6 class="text-dark-gray mt-4">update and personalize microsites</h6>
                    </div>
                    <div class="flex-between align-center ml-30">
                        <div class="icon ic-xxs ic-coal mr-50 ph-mr-20"
                            [ngClass]="isMicrositeOpen ? 'ic-triangle-up' : 'ic-triangle-down'"></div>
                    </div>
                </div>
                <ng-container *ngIf="isMicrositeOpen">
                    <div class="bg-white pl-20 py-16 mt-8 flex-between br-6">
                        <div>
                            <h5 class="fw-600">Enquiry Form</h5>
                            <h6 class="text-dark-gray pt-4">you can enable or disable the enquiry form for your tenant
                            </h6>
                        </div>
                        <div class="align-center mr-50 ph-mr-20 ml-20">
                            <div class="text-xs mr-8">{{propertySettingsForm.get('shouldEnableEnquiryForm').value
                                == true ? 'on' : 'off'}}</div>
                            <input type="checkbox" class="toggle-switch toggle-active-sold"
                                (click)="canUpdate ? openConfirmModal(changePopup, 'shouldEnableEnquiryForm') : ''"
                                formControlName='shouldEnableEnquiryForm' id="chkshouldEnableEnquiryForm"
                                name="shouldEnableEnquiryForm" [ngClass]="{'pe-none' : !canUpdate}">
                            <label for='chkshouldEnableEnquiryForm' class="switch-label"
                                [ngClass]="{'pe-none' : !canUpdate}"></label>
                        </div>
                    </div>
                </ng-container>
                <ng-container *ngIf="isMicrositeOpen">
                    <div class="bg-white pl-20 py-16 mt-8 flex-between br-6">
                        <div>
                            <h5 class="fw-600">Show more properties</h5>
                            <h6 class="text-dark-gray pt-4">You can enable or disable the show more properties for your tenant
                            </h6>
                        </div>
                        <div class="align-center mr-50 ph-mr-20 ml-20">
                            <div class="text-xs mr-8">{{propertySettingsForm.get('showMoreMicrositeProperties').value
                                == true ? 'on' : 'off'}}</div>
                            <input type="checkbox" class="toggle-switch toggle-active-sold"
                                (click)="canUpdate ? openConfirmModal(changePopup, 'showMoreMicrositeProperties') : ''"
                                formControlName='showMoreMicrositeProperties' id="chkShowMoreMicrositePropertiesEnabled"
                                name="showMoreMicrositeProperties" [ngClass]="{'pe-none' : !canUpdate}">
                            <label for='chkShowMoreMicrositePropertiesEnabled' class="switch-label"
                                [ngClass]="{'pe-none' : !canUpdate}"></label>
                        </div>
                    </div>
                </ng-container>
            </div>
        </form>
    </div>
    <ng-template #changePopup>
        <div class="p-20">
            <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
            <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div>
            <div class="flex-end mt-30">
                <button class="btn-gray mr-20" (click)="closePopup()" id="clkSettingsNo"
                    data-automate-id="clkSettingsNo">
                    {{ 'GLOBAL.no' | translate }}</button>
                <button class="btn-green" (click)="onSave()" id="clkSettingsYes" data-automate-id="clkSettingsYes">
                    {{ 'GLOBAL.yes' | translate }}</button>
            </div>
        </div>
    </ng-template>
</ng-container>