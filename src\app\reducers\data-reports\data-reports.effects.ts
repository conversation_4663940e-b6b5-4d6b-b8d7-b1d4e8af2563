import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { map, switchMap, catchError } from 'rxjs/operators';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import {
    DataReportsActionTypes,
    ExportDataActivity,
    ExportDataActivitySuccess,
    ExportDataCall,
    ExportDataCallSuccess,
    ExportDataProjectStatus,
    ExportDataProjectStatusSuccess,
    ExportDataSourceStatus,
    ExportDataSourceStatusSuccess,
    ExportDataSubSourceStatus,
    ExportDataSubSourceStatusSuccess,
    ExportDataUserStatus,
    ExportDataUserStatusSuccess,
    FetchDataActivityExport,
    FetchDataActivityExportSuccess,
    FetchDataCallExportSuccess,
    FetchDataProjectExport,
    FetchDataProjectExportSuccess,
    FetchDataReportsActivity,
    FetchDataReportsActivityCommunication,
    FetchDataReportsActivityCommunicationSuccess,
    FetchDataReportsActivitySuccess,
    FetchDataReportsActivityTotalCount,
    FetchDataReportsActivityTotalCountSuccess,
    FetchDataReportsCall,
    FetchDataReportsCallSuccess,
    FetchDataReportsCallTotalCount,
    FetchDataReportsCallTotalCountSuccess,
    FetchDataReportsProject,
    FetchDataReportsProjectSuccess,
    FetchDataReportsProjectTotalCount,
    FetchDataReportsProjectTotalCountSuccess,
    FetchDataReportsSource,
    FetchDataReportsSourceSuccess,
    FetchDataReportsSourceTotalCount,
    FetchDataReportsSourceTotalCountSuccess,
    FetchDataReportsSubSource,
    FetchDataReportsSubSourceSuccess,
    FetchDataReportsSubSourceTotalCount,
    FetchDataReportsSubSourceTotalCountSuccess,
    FetchDataReportsUser,
    FetchDataReportsUserSuccess,
    FetchDataReportsUserTotalCount,
    FetchDataReportsUserTotalCountSuccess,
    FetchDataSourceExport,
    FetchDataSourceExportSuccess,
    FetchDataSubSourceExport,
    FetchDataSubSourceExportSuccess,
    FetchDataUserExport,
    FetchDataUserExportSuccess,
    FetchDataReportsAgency,
    FetchDataReportsAgencySuccess,
    FetchDataReportsAgencyTotalCount,
    FetchDataReportsAgencyTotalCountSuccess,
    FetchDataAgencyExport,
    FetchDataAgencyExportSuccess,
    ExportDataAgency,
    ExportDataAgencySuccess,
} from './data-reports.action';
import { AppState } from 'src/app/app.reducer';
import { OnError } from 'src/app/app.actions';
import {
    getDataActivityFiltersPayload,
    getDataAgencyFiltersPayload,
    getDataCallFiltersPayload,
    getDataProjectFiltersPayload,
    getDataSourceFiltersPayload,
    getDataSubSourceFiltersPayload,
    getDataUserFiltersPayload,
} from './data-reports.reducers';
import {
    FetchReportsActivityTotalCount,
    FetchReportsActivity9,
    FetchReportsActivity10,
    FetchReportsActivity11,
    FetchReportsActivity12,
} from '../reports/reports.actions';
import { CommonService } from 'src/app/services/shared/common.service';
import { DataReportsService } from 'src/app/services/controllers/data-reports.service';
@Injectable()
export class DataReportsEffects {
    getDataReportsUsers$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_USER),
            map((action: FetchDataReportsUser) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getDataUserFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                });
                this.store.dispatch(new FetchDataReportsUserTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchDataReportsUserSuccess(resp);
                        }
                        return new FetchDataReportsUserSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getDataReportsUsersTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_USER_TOTAL_COUNT),
            map((action: FetchDataReportsUserTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getDataUserFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                    filterPayload = { ...data, path: 'datareport/user/status-count' };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchDataReportsUserTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getDataUserExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_USER_EXPORT),
            map((action: FetchDataUserExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchDataUserExportSuccess(resp.data);
                        }
                        return new FetchDataUserExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportDataUserStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.EXPORT_DATA_USER_STATUS),
            switchMap((action: ExportDataUserStatus) => {
                return this.api.exportUserDataStatus(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportDataUserStatusSuccess(resp);
                        }
                        return new ExportDataUserStatusSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    //project
    getDataReportsProjects$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_PROJECT),
            map((action: FetchDataReportsProject) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getDataProjectFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                    });
                this.store.dispatch(new FetchDataReportsProjectTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchDataReportsProjectSuccess(resp);
                        }
                        return new FetchDataReportsProjectSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getDataReportsProjectsTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_PROJECT_TOTAL_COUNT),
            map((action: FetchDataReportsProjectTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getDataProjectFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                        filterPayload = {
                            ...data,
                            path: 'datareport/project/status-count',
                        };
                    });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchDataReportsProjectTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getDataProjectExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_PROJECT_EXPORT),
            map((action: FetchDataProjectExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchDataProjectExportSuccess(resp.data);
                        }
                        return new FetchDataProjectExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportDataProjectStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.EXPORT_DATA_PROJECT_STATUS),
            switchMap((action: ExportDataProjectStatus) => {
                return this.api.exportProjectDataStatus(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportDataProjectStatusSuccess(resp);
                        }
                        return new ExportDataProjectStatusSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    //SOURCE

    getDataReportsSources$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_SOURCE),
            map((action: FetchDataReportsSource) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getDataSourceFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                    });
                this.store.dispatch(new FetchDataReportsSourceTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchDataReportsSourceSuccess(resp);
                        }
                        return new FetchDataReportsSourceSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getDataReportsSourcesTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_SOURCE_TOTAL_COUNT),
            map((action: FetchDataReportsSourceTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getDataSourceFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                        filterPayload = { ...data, path: 'datareport/source/status-count' };
                    });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchDataReportsSourceTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getDataSourceExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_SOURCE_EXPORT),
            map((action: FetchDataSourceExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchDataSourceExportSuccess(resp.data);
                        }
                        return new FetchDataSourceExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportDataSourceStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.EXPORT_DATA_SOURCE_STATUS),
            switchMap((action: ExportDataSourceStatus) => {
                return this.api.exportSourceDataStatus(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportDataSourceStatusSuccess(resp);
                        }
                        return new ExportDataSourceStatusSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    //subsource
    getDataReportsSubSources$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_SUB_SOURCE),
            map((action: FetchDataReportsSubSource) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getDataSubSourceFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                    });
                this.store.dispatch(new FetchDataReportsSubSourceTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchDataReportsSubSourceSuccess(resp);
                        }
                        return new FetchDataReportsSubSourceSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getDataReportsSubSourcesTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_SUB_SOURCE_TOTAL_COUNT),
            map((action: FetchDataReportsSubSourceTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getDataSubSourceFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                        filterPayload = {
                            ...data,
                            path: 'datareport/subsource/status-count',
                        };
                    });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchDataReportsSubSourceTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getDataSubSourcesExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_SUB_SOURCE_EXPORT),
            map((action: FetchDataSubSourceExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchDataSubSourceExportSuccess(resp.data);
                        }
                        return new FetchDataSubSourceExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportDataSubSourceStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.EXPORT_DATA_SUB_SOURCE_STATUS),
            switchMap((action: ExportDataSubSourceStatus) => {
                return this.api.exportSubSourceDataStatus(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportDataSubSourceStatusSuccess(resp);
                        }
                        return new ExportDataSubSourceStatusSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    //call

    getDataReportsCall$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_CALL),
            map((action: FetchDataReportsCall) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getDataCallFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                });
                this.store.dispatch(new FetchDataReportsCallTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchDataReportsCallSuccess(resp);
                        }
                        return new FetchDataReportsCallSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getDataReportsCallTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_CALL_TOTAL_COUNT),
            map((action: FetchDataReportsCallTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getDataCallFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                    filterPayload = {
                        ...data,
                        path: 'datareport/user/call-log/new-count',
                    };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchDataReportsCallTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getDataCallExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_CALL_EXPORT),
            map((action: FetchDataSubSourceExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchDataCallExportSuccess(resp.data);
                        }
                        return new FetchDataCallExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportDataCall$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.EXPORT_DATA_CALL),
            switchMap((action: ExportDataCall) => {
                return this.api.exportCallData(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportDataCallSuccess(resp);
                        }
                        return new ExportDataCallSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    //activity
    getDataReportsActivity$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_ACTIVITY_REPORTS),
            map((action: FetchDataReportsActivity) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getDataActivityFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = {
                            ...data,
                            path: 'datareport/activity/all',
                        };
                    });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchDataReportsActivitySuccess(resp);
                        }
                        return new FetchDataReportsActivitySuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getDataReportsActivityCommunication$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_ACTIVITY_COMMUNICATION_REPORTS),
            map((action: FetchDataReportsActivityCommunication) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getDataActivityFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = {
                            ...data,
                            path: 'datareport/activity/communication',
                        };
                    });
                this.store.dispatch(new FetchDataReportsActivity());
                this.store.dispatch(new FetchDataReportsActivityTotalCount());
                if (data.section == 'All') {
                    this.store.dispatch(new FetchReportsActivity9());
                    this.store.dispatch(new FetchReportsActivity10());
                    this.store.dispatch(new FetchReportsActivity11());
                    this.store.dispatch(new FetchReportsActivity12());
                    // this.store.dispatch(new FetchReportFlagCount(filterPayload));
                    this.store.dispatch(new FetchReportsActivityTotalCount());
                }
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchDataReportsActivityCommunicationSuccess(resp);
                        }
                        return new FetchDataReportsActivityCommunicationSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getDataReportsActivityTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_ACTIVITY_REPORTS_TOTAL_COUNT),
            map((action: FetchDataReportsActivityTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getDataActivityFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                        filterPayload = { ...data, path: 'datareport/activity/count' };
                    });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchDataReportsActivityTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getDataActivityExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_ACTIVITY_REPORTS_EXPORT),
            map((action: FetchDataActivityExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchDataActivityExportSuccess(resp.data);
                        }
                        return new FetchDataActivityExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportDataActivity$ = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.EXPORT_DATA_ACTIVITY),
            switchMap((action: ExportDataActivity) => {
                return this.api.exportDataActivity(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportDataActivitySuccess();
                        }
                        return new ExportDataActivitySuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    fetchDataReportsAgency = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_AGENCY),
            map((action: FetchDataReportsAgency) => action),
            switchMap((action: any) => {
                let filterPayload;
                this.store.select(getDataAgencyFiltersPayload).subscribe((data: any) => {
                    filterPayload = { ...data, path: 'datareport/agency/status' };
                });
                // Only call total count if it's not just a pagination change
                if (!action.skipTotalCount) {
                    this.store.dispatch(new FetchDataReportsAgencyTotalCount());
                }
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((response: any) => {
                        return new FetchDataReportsAgencySuccess(response);
                    }),
                    catchError((error: any) => {
                        this.store.dispatch(new OnError(error));
                        return of(new FetchDataReportsAgencySuccess());
                    })
                );
            })
        )
    );

    fetchDataReportsAgencyTotalCount = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_AGENCY_TOTAL_COUNT),
            map((action: FetchDataReportsAgencyTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getDataAgencyFiltersPayload).subscribe((data: any) => {
                    filterPayload = { ...data, path: 'datareport/agency/status-count' };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((response: any) => {
                        return new FetchDataReportsAgencyTotalCountSuccess(response);
                    }),
                    catchError((error: any) => {
                        this.store.dispatch(new OnError(error));
                        return of(new FetchDataReportsAgencyTotalCountSuccess());
                    })
                );
            })
        )
    );

    fetchDataAgencyExport = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.FETCH_DATA_REPORTS_AGENCY_EXPORT),
            map((action: FetchDataAgencyExport) => action),
            switchMap((action: any) => {
                return this.api.getDataReportsAgencyExport(action.payload).pipe(
                    map((response: any) => {
                        return new FetchDataAgencyExportSuccess(response);
                    }),
                    catchError((error: any) => {
                        this.store.dispatch(new OnError(error));
                        return of(new FetchDataAgencyExportSuccess());
                    })
                );
            })
        )
    );

    exportDataAgency = createEffect(() =>
        this.actions.pipe(
            ofType(DataReportsActionTypes.EXPORT_DATA_AGENCY_STATUS),
            map((action: ExportDataAgency) => action),
            switchMap((action: any) => {
                return this.api.getDataReportsAgencyExport(action.payload).pipe(
                    map((response: any) => {
                        this._notificationService.success('Export request submitted successfully!');
                        return new ExportDataAgencySuccess(response);
                    }),
                    catchError((error: any) => {
                        this.store.dispatch(new OnError(error));
                        return of(new ExportDataAgencySuccess());
                    })
                );
            })
        )
    );

    constructor(
        private actions: Actions,
        private store: Store<AppState>,
        private api: DataReportsService,
        private _notificationService: NotificationsService,
        private commonService: CommonService
    ) { }
}
