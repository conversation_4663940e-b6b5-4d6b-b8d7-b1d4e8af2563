export enum IntegrationSource {
  'Direct' = 0,
  'IVR',
  'Facebook',
  'LinkedIn',
  'GoogleAds',
  'MagicBricks',
  'NinetyNineAcres',
  'Housing',
  'GharOffice',
  'Referral',
  'WalkIn',
  'Website',
  'Gmail',
  'Microsite',
  'Portfolio',
  'Phonebook',
  'CallLogs',
  'LeadPool',
  'SquareYards',
  'QuikrHomes',
  'JustLead',
  'WhatsApp',
  'YouTube',
  'QRCode',
  'Instagram',
  'OLX',
  'EstateDekho',
  'GoogleSheets',
  'ChannelPartner',
  'RealEstateIndia',
  'CommonFloor',
  'Data',
  'RoofandFloor',
  'MicrosoftAds',
  'PropertyWala',
  'ProjectMicrosite',
  'MyGate',
  'Flipkart',
  'PropertyFinder',
  'Bayut',
  'Dubizzle',
  'Webhook',
  'TikTok',
  'Snapchat',
  'GoogleCampaign',
}
export enum LeadSource {
  Direct = 0,
  IVR,
  Facebook,
  LinkedIn,
  'Google Ads',
  'Magic Bricks',
  '99 Acres',
  'Housing.com',
  GharOffice,
  Referral,
  'Walk In',
  Website,
  'Gmail',
  'Microsite',
  'Portfolio',
  'Phonebook',
  'Call Logs',
  'Lead Pool',
  'SquareYards',
  'QuikrHomes',
  'JustLead',
  'WhatsApp',
  'YouTube',
  'QR Code',
  'Instagram',
  'OLX',
  'Estate Dekho',
  'Google Sheets',
  'Channel Partner',
  'Real Estate India',
  'Common Floor',
  'Data',
  'Roof & Floor',
  'Microsoft Ads',
  'PropertyWala',
  'Project Microsite',
  'MyGate',
  'Flipkart',
  'PropertyFinder',
  'Bayut',
  'Dubizzle',
  'Webhook',
  'TikTok',
  'Snapchat',
}
export enum LeadDateType {
  'All' = 0,
  'Created Date',
  'Scheduled Date',
  'Modified Date',
  'Deleted Date',
  'Possession Date',
  'Picked Date',
  'Booked Date',
  'Assigned Date',
}

export enum ReportDateType {
  'All' = 0,
  'Created Date',
  'Scheduled Date',
  'Modified Date',
  'Booked Date',
}
export enum PropertyDateType {
  'All' = 0,
  'Created Date',
  'Modified Date',
  'Possession Date',
}

export enum LeadStatus {
  'New' = 0,
  'Pending',
  'Callback',
  'Schedule Meeting',
  'Meeting Done',
  'Schedule Site Visit',
  'Visit Done',
  'Book',
  'Not Interested',
  'Drop',
}
export enum LeadVisibility {
  'All' = 0,
  'My Leads',
  "Team's",
  'Unassigned',
  'Deleted',
  'Duplicate',
}
export enum EnquiryType {
  None = 0,
  Buy,
  Sale,
  Rent,
}
export enum BHKType {
  'None' = 0,
  'Simplex',
  'Duplex',
  'PentHouse',
  'Others',
}
export enum PropertyType {
  Residential = 0,
  Commercial,
  Agricultural,
}

export enum SaleType {
  None = 0,
  New,
  Resale,
}
export enum Priority {
  Low = 0,
  Medium,
  High,
  Critical,
}
export enum FurnishStatus {
  Unknown = 0,
  Unfurnished,
  'Semi-Furnished',
  Furnished,
}
export enum Facing {
  'Unknown' = 0,
  'East',
  'West',
  'North',
  'South',
  'North-East',
  'North-West',
  'South-East',
  'South-West',
}
export enum PropertyStatus {
  Active = 0,
  Sold,
}
export enum PropertyPriceFilter {
  'Upto 10 lakhs' = 0,
  '10 Lakhs - 20 Lakhs',
  '20 Lakhs - 30 Lakhs',
  '30 Lakhs - 40 Lakhs',
  '40 Lakhs - 50 Lakhs',
  '50 lakhs to 1 Crore',
  'More than 1 Crore',
}
export enum UserActions {
  ADD_TASK = 'Add Task',
  ADD_PROPERTY = 'Add Property',
  ADD_LEAD = 'Add Lead',
  MANAGE_DATA = 'Manage Data',
  BULK_UPLOAD = 'Bulk Upload',
  MANAGE_TASK = 'Managing Todo',
  MANAGE_PROPERTY = 'Managing Property',
  SHARING_PROPERTY = 'Sharing Property',
}
export enum AreaUnit {
  'Sq. Feet' = 0,
  'Sq. Yards',
  'Sq. Meters',
  Acres,
  Marla,
  Cents,
  Bigha,
  Kattah,
  Kanal,
  Grounds,
  Ares,
  Biswa,
  Guntha,
  Aankadam,
  Hectares,
  Rood,
  Chataks,
  Perch,
}
export enum PropertyAttributeFieldType {
  Bool = 0,
  Enum,
  Int,
}
export enum TodoFilterType {
  All = 0,
  Today,
  Upcoming,
  Completed,
  Overdue,
}
export enum TodoPriority {
  Low = 0,
  Medium,
  High,
  Critical,
}
export enum FolderNamesS3 {
  OrgBrochure = 'orgBrochure',
  Awards = 'awards',
  Images = 'images',
  Recognition = 'recognition',
  IdentityDocs = 'identityDocs',
  LeadDocument = 'leadDocument',
  Property = 'property',
  PropertyDocument = 'propertyDocument',
  WhatsApp = 'whatsApp',
  Project = 'project',
  ProjectUnit = 'projectUnit',
  Reports = 'reports',
}
export enum CallType {
  Inbound = 0,
  Outbound,
}
export enum ProfileImageType {
  BannerImage = 'BannerImage',
  LogoImage = 'LogoImage',
}
export enum DocumentType {
  Default = 0,
  Identity,
  Experience,
  Signature,
}

export enum DoneStatus {
  None = 0,
  'Meeting Done',
  'Meeting Not Done',
  'Site Visit Done',
  'Site Visit Not Done',
}

export enum FileUploadStatus {
  Initiated = 0,
  Started,
  InProgress,
  Completed,
  Failed,
}

export enum SourceChartEnum {
  new = 0,
  booked,
  site_visit_scheduled,
  meeting_scheduled,
  not_interested,
  dropped,
}

export enum LeadScheduledType {
  None = 0,
  Meetings,
  'Site visits',
  Callbacks,
}

export enum LeadScheduledDateTypeFilter {
  All = 0,
  'Scheduled Today',
  'Scheduled Tomorrow',
  'Scheduled next 2 days',
  'Upcoming Schedules',
}

export enum SecondLevelFilter {
  None = 0,
  New,
  Pending,
  Scheduled,
  Overdue,
  Booked,
  All,
  'Different Requirements',
  'Plan Postponed',
  'Different Location',
  'Unmatched Budget',
  'Not Looking',
  'Wrong/invalid No.',
  'Purchased From Others',
  'Ringing Not Received',
  'Expression Of Interest',
}

export enum FirstLevelFilter {
  'All Leads' = 0,
  'Active Leads',
  'Not Interested',
  'Dropped',
  'Booked',
  'Booking Cancel',
  'Invoiced',
}

export enum LeadAssignmentType {
  WithHistory = 0,
  WithoutHistory,
  WithoutHistoryWithNewStatus,
}

export enum Profession {
  None = 0,
  Salaried,
  Business,
  SelfEmployed,
  Doctor,
  Retired,
  Housewife,
  Student,
  Unemployed,
  Others
}

export enum UserStatus {
  All = 0,
  Active,
  InActive,
}

export enum ErrorActionCode {
  NoOp = 100,
  StayOn,
  ChangeRoute,
  FallBack,
  ReturnToHome,
  Refresh,
  Logout,
}

export enum DataFilterType {
  All = 0,
  'Follow Ups',
  Backlog,
  New,
  Qualified,
  'Not Reachable',
  'Not Interested',
  'Not Answered',
  'Invalid',
}

export enum DataDateType {
  All = 0,
  'Created Date',
  'Modified Date',
  'Deleted Date',
  'Schedule Date',
  'Qualified Date',
  'Converted Date',
  'Possession Date',
  'Restored Date',
}

export enum DataDateFilters {
  All = 0,
  'Created Date',
  'Schedule Date',
  'Modified Date',
}

export enum BillingType {
  None,
  Quarterly,
  Halfyearly,
  Yearly,
}

// export enum PaymentMode {
//   UPI,
//   Cashfree,
//   IMPS,
//   NEFT,
//   RTGS,
//   Cheque,
//   Cash,
//   PhonePe,
//   International
// }

export enum ModuleName {
  Lead,
  Todo,
  Integration,
  User,
  Profile,
  Project,
  Property,
  Team,
  Email,
  Invoice,
  Unit,
}

export enum ContactType {
  WhatsApp,
  Call,
  Email,
  SMS,
  PushNotification,
}

export enum UserType {
  None = 0,
  Primary,
  Secondary,
  Both,
}

export enum DateRange {
  Today = 0,
  Yesterday,
  Last7Days,
  CurrentMonth,
  TillDate,
  Custom,
}

export enum LeadFrequency {
  Hour = 0,
  Day,
  Week,
  Month,
  Quarter,
  Year,
}

export enum PossessionType {
  'None' = 0,
  'Under Construction',
  '6 Months',
  '1 Year',
  '2 Years',
  'Custom Date',
  'Immediate',
}

export enum ProjectVisibilityType {
  All,
  Residential,
  Commercial,
  Agriculture,
  Deleted,
}

export enum ProjectStatus {
  Unknown = 0,
  Upcoming,
  Ongoing,
  ReadyToMove,
  New,
  Resale,
  PreLaunch,
  Launch,
}
export enum BookingPaymentMode {
  None = 0,
  Cheque,
  DD,
  IMPS,
  NEFT,
  UPI,
  RTGS,
  Cash,
}

export enum PaymentType {
  None = 0,
  'Bank Loan',
  'Pending Loan Approval',
  'Partial Loan Cash',
  'Loan Applied',
  'Online Transfer',
  'Cash',
  'Cheque',
  'DD',
}

export enum bookedDocumentType {
  None = 0,
  LeadImage,
  Aadhar,
  Pancard,
  Passport,
}

export enum LeadGeneratingFrom {
  All = 0,
  Manual,
  Duplicate,
  Integration,
  BulkUpload,
}

export enum OTPReceiver {
  Self = 0,
  Admin,
  Manager,
  Specific,
}

export enum WaterMarkPositions {
  'Center',
  'Left Top',
  'Right Top',
  'Left Bottom',
  'Right Bottom',
  'Left Center',
  'Right Center',
  'Top Center',
  'Bottom Center',
}

export enum BulkuploadDuplicateOptions {
  None = 0,
  CreateDuplicateLead,
  OverideExisitingLeadInformation,
  UpdateMissingInformation,
  CreateSeparateEnquiry,
}

export enum BulkuploadDuplicateSubOptions {
  All = 0,
  ParentOnly,
  LatestChildLead,
}

export enum WhatsAppEvents {
  None = 0,
  Sent,
  Delivered,
  Read,
  Failed,
  Receive,
}

export enum WhatsAppHeaderTypes {
  Text = 0,
  Image,
  Video,
  Document,
}

export enum FilterType {
  SiteVisits = 0,
  BookedLeads = 1,
  TotalBrokerage = 2,
}

export enum WAButtonType {
  None = 0,
  QuickReply,
  PHONE_NUMBER,
  URL,
  COPY_CODE,
}

export enum BulkType {
  None = 0,
  BulkAssignment,
  BulkUpdateStatus,
  BulkDelete,
  BulkEmail,
  BulkSource,
  BulkProject,
  BulkWhatsApp,
  BulkUpload,
  BulkMigrate,
}

export enum OwnerSelectionType {
  Both = 0,
  'Primary Owner',
  'Secondary Owner',
}

export enum RequestType {
  None = 0,
  Whatsapp,
  IVR,
  Email,
}

export enum MarketingType {
  None = 0,
  AgencyName,
  ChannelPartner,
  CampaignName,
}

export enum PFRequestType {
  None = 0,
  Whatsapp,
  Call,
  Email
}

export enum FieldType {
  None = 0,
  Text,
  Number,
  Email,
  TextArea,
  SingleSelectionDropdown,
  MultipleSelectionDropdown,
  Date,
  Time,
  DateTime,
  Checkbox,
  Radio,
}

export enum OfferType {
  None = 0,
  Ready,
  'Off Plan',
  Secondary,
}

export enum ListingVisibility {
  All,
  Draft,
  Approved,
  Refused,
  Archived,
  Sold,
}

export enum ListingFirstLevelFilter {
  All,
  Ready,
  OffPlan,
  Secondary,
}

export enum ListingSecondLevelFilter {
  All,
  Residential,
  Commercial,
  Agricultural,
}

export enum ListingStatus {
  None,
  Approved,
  Draft,
  Refused,
  Sold,
  Archived,
}

export enum OfferingType {
  None,
  Ready,
  OffPlan,
  Secondary,
}

export enum CompletionStatus {
  None,
  Completed,
  OffPlan,
  CompletedPrimary,
  OffPlanPrimary,
}

export enum PaymentFrequency {
  None,
  Daily,
  Weekly,
  Monthly,
  Yearly,
}

export enum ListingLevel {
  None,
  Standard,
  Featured,
  Premium,
}

export enum TaxationMode {
  GSTInclusive,
  GSTExclusive,
}

export enum WhatsappThrough {
  AskEveryTime,
  TemplateShare,
  OpenConversation,
}

export enum PurposeType {
  None = 0,
  Investment,
  'Self Use'
}

export enum AssignmentCategoryType {
  SequentialBased = 0,
  PercentageBased,
}

export enum UserAssignmentType {
  User = 0,
  Team,
  Project,
  Property
}

export enum Radius {
  None = 0,
  Meter,
  Kilometer
}

export enum CallDirections {
  None = 0,
  Incoming,
  Outgoing,
}

export enum CallStatuses {
  None = 0,
  Missed,
  Disconnected,
  Answered,
}

export enum FinishingType {
  None,
  FullyFinished,
  SemiFinished,
  Unfinished,
}

export enum UaeEmirate {
  None,
  Dubai,
  'Abu Dhabi',
  'Northern Emirates'
}

export enum EventType {
  None = 0,
  'Lead Created',
  'Lead Updated',
}

export enum Gender {
  NotMentioned = 0,
  Male,
  Female,
  Other

}

export enum MaritalStatusType {
  NotMentioned = 0,
  Single,
  Married
}
