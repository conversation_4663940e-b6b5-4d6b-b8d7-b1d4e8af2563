.copy-prevent {
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

$font-family: "Lexend Deca";
$family: "Lexend Deca";
$font-family-base: "Lexend Deca", "DM Sans", sans-serif;
$base-font-size: 11;

body {
  background-color: var(--bg-secondary);
  font-family: "Lexend Deca", "DM Sans", sans-serif;
  @extend .text-normal;
}

.main-container {
  display: flex;
  min-height: 100%;
  position: relative;
}

.app-container {
  @extend .flex-grow-1;
}

.display-mble {
  display: none !important;
}

.cursor-default {
  cursor: default !important;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-not-allow {
  cursor: not-allowed !important;
}

.outline-0 {
  outline: none;
}

.box-shadow-1 {
  box-shadow: 0px 0px 1px 1px $black-40;
}

.box-shadow-2 {
  box-shadow: 1px 1px 2px 0px $black-100;
}

.box-shadow-3 {
  box-shadow: 0px -2px 10px 0px #00000024;
}

.box-shadow-4 {
  box-shadow: 0px 4px 4px 0px $black-60;
}

.box-shadow-5 {
  box-shadow: 0 -2px 4px $black-30;
}

.box-shadow-10 {
  box-shadow: 0px 0px 10px 2px $black-40;
}

.box-shadow-20 {
  box-shadow: 0px 0px 20px 0px $black-30;
}

.box-shadow-30 {
  box-shadow: 0px 4px 20px 2px $black-400;
}

.box-shadow-40 {
  box-shadow: 0px 4px 16px 0px $black-700;
}

.box-shadow-50 {
  box-shadow: 0px 1px 2px 0px #1F293714;
}


ul {
  @extend .p-0;
  @extend .m-0;

  li {
    list-style-type: none;

    a {
      text-decoration: none;
    }
  }
}

.fv-sm-caps {
  font-variant: small-caps;
}

// To Hide Arrows From Input Number
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

a {
  text-decoration: none;
  @extend .cursor-pointer;

  &.text-link {
    color: $blue-20 !important;
  }
}

.text-nowrap {
  white-space: nowrap;
}

.line-break {
  white-space: pre-line;
}

.pre-whitespace {
  white-space: pre-wrap;
}

.break-word {
  word-wrap: break-word;
}

.word-break {
  word-break: break-word;
}

.break-all {
  word-break: break-all;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-height-initial {
  line-height: initial;
}


.lh-21px {
  line-height: 21px !important;
}

.text-truncate-1,
.text-truncate-2,
.text-truncate-3,
.text-truncate-4 {
  display: -webkit-box;
  overflow: hidden;
  white-space: initial !important;
  /*! autoprefixer: ignore next */
  -webkit-box-orient: vertical;
}

.text-truncate-1 {
  -webkit-line-clamp: 1;
}

.text-truncate-2 {
  -webkit-line-clamp: 2;
}

.text-truncate-3 {
  -webkit-line-clamp: 3;
}

.text-truncate-4 {
  -webkit-line-clamp: 4;
}

.opacity-5 {
  opacity: 0.5;
}

.opacity-6 {
  opacity: 0.6;
}

.w-50px {
  width: 50px !important;
}

.w-max-content {
  width: max-content !important;
}

.error-message-custom {
  @extend .position-absolute, .right-20, .fw-semi-bold, .text-xxs;
  color: $red-10;
}

.error-message {
  @extend .error-message-custom, .right-0;
  bottom: -15px;
}

.error {
  .error-message {
    @extend .position-absolute, .left-0, .fw-semi-bold, .text-xxs, .text-wrap;
    bottom: -22px;
  }
}

.error-right {
  .error-message {
    @extend .error-message-custom,
    .text-wrap,
    .text-truncate-2,
    .right-20,
    .tb-bottom-15;
    bottom: -25px;
  }
}

.border-dashed {
  border: 1px dashed $dark-400;
}

.border-dashed-bottom-2 {
  border-bottom: 2px dashed $dark-400;
}

.border-dashed-2 {
  border: 2px dashed $dark-400;
}

.border-green-solid {
  border: 1px solid $accent-green !important;
}

.border-green-dashed {
  border: 1px dashed $accent-green !important;
}

.border-green-dashed-2 {
  border: 2px dashed $accent-green !important;
}

.border-gray-dashed {
  border-left: 1px dashed $slate-90;
}

.border-gray-bottom {
  border-bottom: 1px dashed $slate-90;
}

.border-gray-right {
  border-right: 0.1px solid $slate-90;
}

.border-left-dotted {
  border-left: 1px dotted $slate-10;
}

.border-mud-bottom {
  border-bottom: 2px dashed $dark-700;
}

.border-solid-bottom {
  border-bottom: 1px solid $dark-700 !important;
}

.border-mud-top {
  border-top: 2px dashed $dark-700;
}

.cdk-overlay-container {
  z-index: 9999 !important;
}

.cdk-global-scrollblock {
  overflow: hidden !important;
  top: 0 !important;
}

.modal-overflow {
  overflow: auto;
}

.custom-file-input::-webkit-file-upload-button {
  visibility: hidden;
}

.upload-button input[type="file"] {
  @extend .d-none;
}


.border-white-2 {
  border: 2px solid $white;
}

.border-white {
  border: 1px solid $white;
}

.border-white-hover:hover {
  border: 1px solid $white !important;
}

.border-red {
  border: 1px solid $red-10 !important;
}

.border-red-800 {
  border: 1px solid $red-800 !important;
}

.border-black {
  border: 1px solid $black;
}

.border-black-200 {
  border: 1px solid $black-200;
}

.border-black-dashed {
  border: 1px dashed $black;
}

.border-bottom-black {
  border-bottom: 1px solid $black !important;
}

.border-left-black-200 {
  border-left: 1px solid $black-200;
}

.border-left-black-100 {
  border-left: 1px solid $black-100;
}

.border-green-160 {
  border: 1px solid $green-160;
}

.border-green-900 {
  border: 1px solid $green-900;
}

.border-light-green-500 {
  border: 1px solid $light-green-500;
}

.border-bottom-green {
  border-bottom: 1px solid $accent-green !important;
}

.border-green-2 {
  border: 2px solid $accent-green;
}

.border-bottom-slate {
  border-bottom: 1px solid $dark-500;
}

.border-dark-500 {
  border: 0.5px solid $dark-500;
}

.border-slate-40 {
  border: 1px solid $slate-40;
}

.border-slate-60 {
  border: 1px solid $slate-60 !important;
}

.border-slate-110 {
  border: 1px solid $slate-110 !important;
}

.border-bottom-slate-40 {
  border-bottom: 1px solid $slate-40;
}

.border-bottom-slate-20 {
  border-bottom: 1px solid $slate-20;
}

.border-gray {
  border: 1px solid $dark-400;
}

.border-gray-2 {
  border: 2px solid $dark-400;
}

.border-gray-y {
  border-top: 1px solid $dark-400;
  border-bottom: 1px solid $dark-400;
}

.border-dark-600 {
  border-right: 1px solid $dark-600;
}

.border-bottom-dark-600 {
  border-bottom: 1px solid $dark-600;
}

.border-left-orange-800 {
  border-left: 4px solid $orange-800;
}

.border-bottom-blue-300 {
  border-bottom: 1px solid $dark-blue-300;
}

.border-red-30 {
  border: 1px solid $red-30;
}

.border-right-red-110 {
  border-right: 2px solid $red-110;
}

.border-right-red-550 {
  border-right: 2px solid $red-550;
}

.border-right-purple-300 {
  border-right: 2px solid $purple-300;
}

.border-right-yellow-500 {
  border-right: 2px solid $yellow-500;
}

.border-accent-green {
  border: 1px solid $accent-green !important;
}

.border-accent-green-4 {
  border: 4px solid $accent-green !important;
}

.border-left-green-40 {
  border-left: 4px solid $green-40;
}

.border-green-30 {
  border: 1px solid $green-30;
}

.border-right-green-550 {
  border-right: 2px solid $green-550;
}

.border-top {
  border-top: 1px solid $dark-400 !important;
}

.border-right {
  border-right: 1px solid $dark-400;
}

.border-bottom {
  border-bottom: 1px solid $dark-400 !important;
}

.border-left {
  border-left: 1px solid $dark-400;
}

.border-left-blue-50 {
  border-left: 4px solid $blue-50;
}

.border-right-blue-700 {
  border-right: 2px solid $blue-700;
}

.border-right-0 {
  border-right: 0 !important;
}

.scrollbar {
  overflow: auto !important;

  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  &::-webkit-scrollbar-track {
    background: #49494914;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: $dark-850;
    border-radius: 3px;
  }
}

.scrollbar-unset-x {
  overflow-x: hidden !important;
  overflow-y: scroll !important;

  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  &::-webkit-scrollbar-track {
    background: #49494914;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: $dark-850;
    border-radius: 3px;
  }
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scroll-hide::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scroll-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-sm::-webkit-scrollbar {
  width: 50px !important;
  height: 1px !important;
}

.scrollbar-sm::-webkit-scrollbar-thumb {
  background-color: #808080 !important;
  border-radius: 3px !important;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.drag-scroll-content::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.drag-scroll-content {
  -ms-overflow-style: none;
  scrollbar-width: none;
  height: 100% !important;
}

.hidden-phone {
  display: none;
}

.hidden-web {
  display: block;
}

.w-100per {
  width: 100% !important;
}

.w-100vw {
  width: 100vw;
}

.h-24px {
  height: 24px;
}

.h-100vh {
  height: 100dvh;
}

.h-50vh {
  height: 50vh !important;
}

.min-h-100 {
  min-height: 100vh;
}

a {
  text-decoration: none;
  color: inherit;

  &:hover {
    color: inherit;
  }
}

.overflow-y-auto {
  overflow-y: auto;
}

.nz-index-1 {
  z-index: -1;
}

.z-index-2 {
  z-index: 2;
}

.z-index-1001 {
  z-index: 1001;
}

.z-index-1021 {
  z-index: 1021;
}

.z-index-1022 {
  z-index: 1022;
}

//animation for popup modal
.trans-popup {
  -webkit-animation-name: fadeIn;
  -webkit-animation-duration: 0.8s;
  animation-name: fadeIn;
  animation-duration: 0.8s;
}

@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.dropdown {
  .dropdown-custom-x-sm {
    background-color: $white !important;
    border: none;
    color: $black !important;
    display: flex;
    @extend .justify-content-between;
    padding: 3px;

    &::after {
      font-family: "leadRat" !important;
      content: "\e919" !important;
      border-top: none !important;
      border-radius: 6px;
      color: $black !important;
      margin-left: 10px;
      @extend .ic-xxs;
    }

    &.dropdown-sm {
      padding: 5px 0px;

      &::after {
        margin: 0px 10px;
        color: $slate-500 !important;
        @extend .ic-xxxs;
      }
    }
  }

  .dropdown-menu {
    min-width: 100px;

    .dropdown-item {
      padding: 8px 16px !important;

      &:hover {
        @extend .cursor-pointer;
        background: $accent-green !important;
        color: $white !important;
      }

      &:focus {
        background-color: none !important;
      }
    }
  }
}

.grid-dropdown {
  .dropdown-menu {
    min-width: 100px !important;
    background-color: $black;
    transform: translate(0px, 0px) !important;
    @extend .clear-padding;

    .dropdown-item {
      padding: 0px 12px !important;
      color: $white;

      &:hover {
        background-color: $black !important;
        color: $slate-60 !important;
      }

      &:focus {
        background-color: none !important;
        position: absolute;
      }
    }

    &::-webkit-scrollbar {
      width: 5px;
      height: 5px;
    }

    &::-webkit-scrollbar-track {
      background: #49494914;
    }

    &::-webkit-scrollbar-thumb {
      background: $accent-green;
    }
  }
}

.flag:hover .dropdown {
  visibility: visible !important;
}

//need to check where we are using
.br-10 {
  .modal-content {
    @extend .br-10;
  }
}

.profile-picture {
  img {
    height: 65px;
    width: 65px;
    border-radius: 50%;
  }
}

.input-editable {
  input {
    &:focus {
      box-shadow: none !important;
    }
  }
}

.progress {
  height: 15px;
  border-radius: 50px;

  .progress-bar {
    border-top-right-radius: 50px;
    border-bottom-right-radius: 50px;
  }

  &.progress-accent-blue {
    background-color: $blue-400;

    .progress-bar {
      background-color: $blue-200 !important;
    }
  }

  &.progress-accent-purple {
    background-color: $purple-400;

    .progress-bar {
      background-color: $accent-purple !important;
    }
  }

  &.progress-accent-green {
    background-color: $light-green-200;

    .progress-bar {
      background-color: $accent-green !important;
    }
  }

  &.progress-accent-orange {
    background-color: $orange-50;

    .progress-bar {
      background-color: $accent-orange !important;
    }
  }
}

.badge {
  border-radius: 20px;
  padding: 4px 8px;
}

.status-badge {
  border: 1px solid $dark-700;
  @extend .text-sm,
  .mr-4,
  .mb-8,
  .br-6,
  .cursor-pointer,
  .fw-semi-bold,
  .px-16,
  .py-4,
  .text-center,
  .align-middle;

  &.active {
    border: 1px solid $dark-700 !important;
    @extend .text-white, .bg-dark-700;
  }
}

.calender-date {
  &.active {
    border: 1px solid $dark-700 !important;
    @extend .text-white, .bg-dark-700;
    padding: 0px 5px;
    border-radius: 5px;
  }
}

//tooltip for google charts
div.google-visualization-tooltip {
  background-color: $dark-50;
  border-radius: 5px;
  border: transparent !important;
}

div.google-visualization-tooltip>ul>li>span {
  color: $white !important;
}

.obj-cover {
  object-fit: cover;
}

.obj-fill {
  object-fit: fill;
}

.simple-notification-wrapper {
  z-index: 2000 !important;
}

.simple-notification-wrapper.right {
  right: unset !important;
}

.simple-notification-wrapper.bottom {
  bottom: 0px !important;
}

simple-notifications {
  display: flex;
  align-content: center;
  justify-content: center;
}

.notes {
  position: relative;

  .pointer {
    &::before {
      content: "";
      display: block;
      width: 0;
      height: 0;
      position: absolute;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-bottom: 8px solid $slate-20;
      top: -5px;
    }
  }
}

.dot-gray {
  &::before {
    content: "";
    background-color: $slate-60 !important;
    display: block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border-bottom: 8px solid $slate-60;
    position: relative;
    top: 22px;
    left: -29px;
  }
}

.radio-container {
  .form-check-inline {
    justify-content: flex-start !important;
  }
}

.remove-margin-radio {
  .form-check-inline {
    margin-right: 4px !important;
  }
}

//Home-page Property-No Image
.view {
  position: relative;
  border-radius: 10px;

  .mask {
    width: 130px;
    height: 90px;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    background-color: $black-50;
    transition: all 0.4s ease-in-out;
    border-radius: 10px;

    &:hover {
      opacity: 1;
    }
  }
}

.pulse-animation {
  transform: translate(-50%, -50%);
  animation: pulse 2s infinite;
  z-index: 1002;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
  }

  50% {
    transform: translate(-50%, -50%) scale(1.05);
  }

  100% {
    transform: translate(-50%, -50%) scale(1);
  }
}

.tool {
  position: relative;
  text-align: center;
}

.tool::before,
.tool::after {
  opacity: 0;
  position: absolute;
  -webkit-user-select: none;
  user-select: none;
}

.tool::before {
  content: "";
  border-style: solid;
  border-width: 0 16px 16px;
  border-color: transparent;
  border-bottom-color: $accent-green;
  margin-top: 160px;
  margin-left: -16px;
  transition: all 0.65s cubic-bezier(0.84, -0.18, 0.31, 1.26) 0.2s;
  transform: scale(0.6) translateY(90%);
}

.tool::after {
  content: attr(data-tip);
  background: $accent-green;
  border-radius: 16px;
  color: $white;
  margin-top: 20px;
  margin-left: -110px;
  padding: 16px;
  transition: all 0.65s cubic-bezier(0.84, -0.18, 0.31, 1.26) 0.2s;
  transform: scale(0.6) translateY(-50%);
  width: 215px;
  line-height: 30px;
}

.tool:hover::before,
.tool:hover::after {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.tool-black {
  position: relative;
  text-align: center;

  &:active,
  &:focus,
  &:visited {
    outline: none;

    &:after {
      content: attr(data-tip) !important;
    }

    &:before {
      border-style: solid;
      border-width: 1em 0.75em 0 0.75em;
      border-color: #3e474f transparent transparent transparent;
    }
  }
}

.tool-black::before,
.tool-black::after {
  left: -30%;
  opacity: 0;
  position: absolute;
  z-index: -100;
}

.tool-black:hover::before,
.tool-black:hover::after {
  opacity: 1;
  transform: scale(1) translateY(0);
  z-index: 100;
}

.tool-black::before {
  bottom: 120%;
  content: "";
}

.tool-black::after {
  background: #3e474f;
  border-radius: 0.25em;
  bottom: 170%;
  color: #edeff0;
  margin-left: -3em;
  padding: 0.5em;
  transition: all 0.65s cubic-bezier(0.84, -0.18, 0.31, 1.26) 0.2s;
  transform: scale(0.6) translateY(50%);
  width: 7.5em;
}

.grid-blur {
  opacity: 0.4;
  filter: blur(1.7px);
}

.blur-6 {
  filter: blur(6px);
}

.muso {
  bottom: 16px;
  left: 6px;
  width: 70px;
  height: 70px;
}

.login-container {
  @extend .p-20, .br-10, .shadow;
  background-color: #ffffff;
  // -webkit-backdrop-filter: blur(10px);
  // backdrop-filter: blur(10px);

  &.login-form {
    @extend .float-end, .w-360, .ph-w-300, .p-30, .h-100-80, .m-20;
    background-image: url("../../assets/images/login-pattern.svg");
    background-repeat: no-repeat;
    background-position: right top;
  }

  &.support {
    @extend .px-16, .py-20, .position-absolute;
    bottom: 130px;
    left: 60px;
  }

  &.scrollbar::-webkit-scrollbar-track {
    @extend .my-30;
  }
}

// Colors
$bg-color: #e5e5e5;
$dot-color: #a799cc;

// Dimensions
$dot-size: 1.5px;
$dot-space: 22px;

.workflow-bg {
  overflow: hidden;
  background: linear-gradient(90deg,
      $bg-color (
      $dot-space - $dot-size),
    transparent 1%) center,
    linear-gradient($bg-color (
      $dot-space - $dot-size),
    transparent 1%) center,
    $dot-color;
  background-size: $dot-space $dot-space;
}

.bg-linear-orange {
  background: linear-gradient(135deg, #ff686b 8.92%, #9f1e23 100%),
    #50bea7;
  border-radius: 25.69px;
}

.bg-linear-blue {
  background: linear-gradient(135deg, #68adff 8.92%, #1e619f 100%),
    #50bea7;
  border-radius: 25.69px;
}

.bg-linear-green {
  background: linear-gradient(135deg, #68ffd2 8.92%, #1e889f 100%
  );
border-radius: 19.9926px;
}

.bg-linear-green-10 {
  background: linear-gradient(135deg, #50bfa8 100%, #348d7a 100%);
  border-radius: 19.9926px;
}

.bg-linear-yellow {
  background: linear-gradient(135deg, #ffd568 8.92%, #9f5c1e 99.48%);
  border-radius: 25.69px;
}

.bg-linear-violet {
  background: linear-gradient(180deg, #8117a7fa 0%, #ceacdab3 100%);
  border-radius: 25.69px;
}

.magnifier {
  position: absolute;
  bottom: 2%;
  left: 565px;
}

.cam-icon {
  transition: 0.5s ease;
}

.logo-container:hover .cam-icon {
  opacity: 0.7 !important;
}

.on-hover-show {
  display: none;
}

.on-hover:hover .on-hover-show {
  display: block;
}

.vertical-navbar {
  @extend .position-relative;
  border-right: 2px solid $dark-400;

  .nav-item {
    @extend .align-center, .cursor-pointer, .header-5, .fw-semi-bold;

    &.active {
      @extend .fw-600;

      &::after {
        content: "";
        @extend .position-absolute, .nright-3, .h-35px, .br-5;
        border: 2px solid $primary-black;
      }
    }
  }
}

.Horizontal-navbar {
  @extend .position-relative,
  .align-center,
  .py-10,
  .px-20,
  .ph-px-10,
  .bg-slate-130,
  .br-4;

  .nav-item {
    @extend .align-center,
    .cursor-pointer,
    .header-5,
    .fw-semi-bold,
    .mr-20,
    .ph-mr-12;

    &.active {
      @extend .fw-600, .text-accent-green;

      &::after {
        content: "";
        @extend .position-absolute, .w-24, .bottom-5;
        border: 1px solid $accent-green;
      }
    }
  }
}

.user {
  .activation {
    @extend .cursor-pointer,
    .fw-600,
    .text-large,
    .text-dark-gray,
    .br-20,
    .px-16,
    .ip-px-8,
    .ph-px-16,
    .py-8,
    .m-4,
    .ip-flex-center-col,
    .align-center,
    .text-nowrap;

    &.active {
      @extend .bg-black-200, .text-white;
    }
  }

  .icon {
    &.active {
      @extend .ic-light-gray;
    }
  }
}

.user-info {
  .activation {
    @extend .cursor-pointer, .fw-600, .header-5, .ic-black-200, .text-black-200;

    &.active {
      @extend .text-accent-green, .ic-accent-green;
    }
  }
}

.section-border {
  .activation {
    @extend .cursor-pointer, .fw-400, .header-5, .text-black-200, .mr-50;

    &.active {
      @extend .text-accent-green, .position-relative, .fw-600;

      .down-border {
        @extend .position-absolute,
        .border-green-2,
        .nbottom-10,
        .brtl-3,
        .brtr-3,
        .w-40,
        .left-0;
      }
    }
  }
}

.payment {
  .activation {
    @extend .cursor-pointer,
    .text-sm,
    .text-dark-gray,
    .align-center,
    .text-nowrap,
    .p-10,
    .bg-light-slate,
    .mr-10,
    .br-4;

    &.active {
      @extend .bg-white, .text-accent-green, .border-accent-green;
    }
  }

  .icon {
    &.active {
      @extend .ic-light-gray;
    }
  }
}

.dashboard-source canvas {
  background-color: $white;
}

.dashboard {
  .module-navbar {
    @extend .d-none;
  }

  .dashboard-navbar {
    @extend .d-block;
  }
}

.module {
  .active {
    @extend .fw-semi-bold, .text-accent-green, .position-relative, .flex-center;

    &:after {
      content: "";
      @extend .position-absolute, .br-4;
      border: 2px solid $accent-green;
      top: 23px;
      width: 140%;
    }
  }
}

.tool {
  position: relative;
  text-align: center;
  z-index: 1021;
}

.tool::before,
.tool::after {
  left: 50%;
  opacity: 0;
  position: absolute;
  z-index: -100;
  border-radius: 6px;
}

.tool:hover::before,
.tool:hover::after {
  opacity: 1;
  transform: scale(1) translateY(0);
  z-index: 100;
}

.tool::before {
  border-style: solid;
  border-width: 1em 0.75em 0 1.25em;
  border-color: #3e474f transparent transparent transparent;
  bottom: 108%;
  content: "";
  transition: all 0.65s cubic-bezier(0.84, -0.18, 0.31, 1.26),
    opacity 0.65s 0.5s;
  transform: scale(0.6) translateY(-90%);
}

.tool:hover::before {
  transition: all 0.65s cubic-bezier(0.84, -0.18, 0.31, 1.26) 0.2s;
}

.tool::after {
  background: #3e474f;
  border-radius: 0.25em;
  bottom: 120%;
  color: #edeff0;
  content: attr(data-tip);
  margin-left: -7em;
  padding: 0 0.5em;
  transition: all 0.65s cubic-bezier(0.84, -0.18, 0.31, 1.26) 0.2s;
  transform: scale(0.6) translateY(50%);
  width: 9.5em;
}

.tool:hover::after {
  transition: all 0.65s cubic-bezier(0.84, -0.18, 0.31, 1.26);
}

.line-break {
  white-space: pre-line;
}

.rotate-45 {
  rotate: 45deg;
}

.rotate-90 {
  rotate: 90deg;
}

.rotate-135 {
  rotate: 135deg;
}

.rotate-180 {
  rotate: 180deg;
}

.rotate-270 {
  rotate: 270deg;
}

.bg-triangle-pattern {
  background-image: url("../../assets/images/triangle-blocks-pattern.svg");
  background-repeat: no-repeat;
  background-position: center bottom;
}

.bg-brick-pattern {
  background-image: url("../../assets/images/hexagon-brick-pattern.svg");
  background-repeat: no-repeat;
  background-size: cover;
  height: 100vh;
}

.bg-dot-pattern {
  background-image: url("../../assets/images/qr-form/dots-pattern.svg");
  background-repeat: no-repeat;
  background-size: cover;
}

.bg-side-dot-pattern {
  /* First background image */
  background-image: url("../../assets/images/side-dot-pattern.svg"),
    url("../../assets/images/side-dot-pattern.svg");
  /* First background position */
  background-position: 130px 90px, -220px -270px;
  background-repeat: no-repeat;
  background-size: cover;
}

.bg-profile {
  background-image: url("../../assets/images/profile.svg");
  background-repeat: no-repeat;
  background-size: 80px;
  background-position: bottom right;
}

.radio-button {
  display: inline-block;
  position: relative;
  height: 16px;
  width: 16px;
  border: 1px solid $dark-350;
  border-radius: 50%;
  cursor: pointer;
}

.radio-button.checked {
  background-color: $white;
  border-color: $accent-green;
}

.radio-button:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: $accent-green;
  display: none;
}

.radio-button.checked:before {
  display: block;
}

.responsive-map {
  overflow: hidden;
  position: relative;
}

.responsive-map .map-container {
  height: calc(100vh - 52px) !important;
  width: 100% !important;
}

.responsive-map {
  .remove-text {
    .gmnoprint {
      display: none !important;
    }

    .gmap-holder {
      display: none !important;
    }

    pointer-events: none !important;
  }

  .w-unset {
    .map-container {
      width: 100% !important;
    }
  }

  .map-container {
    width: 100% !important;
  }
}

.custom-flex-row {
  .flex-row {
    @extend .align-center;
  }
}

#dashboard-container .mat-progress-bar {
  height: 10px;
  border-radius: 6px;
}

.mat-progress-bar-buffer {
  background-color: #eaeef6;
}

.progress-sd .mat-progress-bar-fill::after {
  background: $aqua-650;
}

.progress-md .mat-progress-bar-fill::after {
  background: $dark-blue-200;
}

.progress-cb .mat-progress-bar-fill::after {
  background: $orange-350;
}

.progress-ss .mat-progress-bar-fill::after {
  background: $accent-green;
}

.progress-ms .mat-progress-bar-fill::after {
  background: $violet-350;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.bulk-update {
  .modal-footer {
    background-color: unset !important;
    position: fixed !important;
    width: 90%;
  }

  .tb-h-unset,
  .h-100-150 {
    height: calc(100vh - 300px);
    @extend .hmq-h-100-665;
  }
}

@keyframes carouselSlideIn {
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(0);
  }
}

@keyframes carouselSlideOut {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-100%);
  }
}

#dashboard-head .ng-option,
#dashboard-head .ng-dropdown-panel {
  color: $white;
  background: $black;
}

#dashboard-head .ng-option-marked {
  color: $black;
  background-color: $white;
}

#dashboard-head ng-select {
  border: 0.2px solid $slate-40 !important;
  outline: none;
}

#dashboard-container {
  input {
    border: unset !important;
    outline: none;
    text-align: end;

    &::placeholder {
      color: $black !important;
    }
  }

  .report input {
    text-align: start;
  }

  .ng-select {
    .ng-dropdown-panel {
      width: 120px;
    }
  }
}

.w-unset {
  width: unset !important;
}

.table-scrollbar {
  tbody {
    @extend .scrollbar-unset-x;
    display: block;
  }
}

.dashboard {
  .table-scrollbar {
    tbody {
      @extend .scrollbar, .scroll-hide;
      display: block;
    }
  }
}

.table-scrollbar {

  thead tr,
  tbody tr {
    display: table;
    width: 100%;
    table-layout: fixed;
  }
}

.nmt-20 {
  margin-top: -20px;
}

.nmb-4 {
  margin-bottom: -4px !important;
}

.nml-46 {
  margin-left: -46px !important;
}

.card-hover {
  @extend .br-10, .mt-20, .px-30, .py-16, .position-relative;

  &:hover {
    @extend .bg-white, .shadow;
  }
}

.arrow-icon {
  transition: 0.5s ease;
}

.banner-hover:hover .arrow-icon {
  opacity: 0.7 !important;
}

.banner-hover {
  position: relative;

  &:hover {
    @extend .bg-white, .box-shadow-30, .border-white;
  }
}

.connect-hover {
  position: relative;

  &:hover {
    @extend .bg-secondary;
  }
}

.label-hover {
  position: relative;

  &:hover {
    .visiblity-req {
      @extend .d-block;
    }
  }
}

.attendance-bar-linear-gradient {
  background: linear-gradient(90deg, $accent-green 0%, $green-120 100%);
}

.border-remove {
  &.ng-dirty {
    &.ng-valid {
      border: 1px solid $white !important;
    }
  }
}

.rat-loader {
  height: 70px;
  width: 70px;
  animation: 2000ms rat-loader-animation linear infinite;
}

@keyframes rat-loader-animation {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.progress {
  height: 6px !important;
  --bs-progress-bg: none !important;
}

.progress-bar {
  background-color: $accent-green !important;
}

.blinking {
  @extend .pe-none;
  animation: 2000ms blinking-animation linear infinite;
}

.animated-text {
  animation: blinking-animation 2s infinite;
}

@keyframes blinking-animation {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.shimmer {
  display: inline-block;
  width: 80px;
  height: 18px;
  background: linear-gradient(90deg, transparent, #ffffff80, transparent);
  background-size: 200% 100%;
  animation: shimmer 2s linear infinite;
}

.shimmer-multiple {
  @extend .shimmer;
  width: 95%;
  height: 37px;
  background: linear-gradient(90deg,
      transparent,
      rgb(203 203 203 / 50%),
      transparent);
  background-size: 200% 100%;
}

@keyframes shimmer {
  0% {
    background-position: 200%;
  }

  100% {
    background-position: -200%;
  }
}

.version-two {
  .d-none {
    display: block !important;
  }

  .d-block {
    display: none !important;
  }

  .ngx-file-drop__content {
    color: $black-200 !important;
  }
}

.gray-card {
  @extend .mb-10, .mr-10, .bg-secondary, .p-10, .br-5, .max-w-390;
}

.white-card {
  @extend .gray-card;
  background-color: $white !important;
}

.gray-dot {
  @extend .dot, .dot-xxs, .bg-gray-darker, .mx-4;
}

.version-two {
  .d-none {
    display: block !important;
  }

  .d-block {
    display: none !important;
  }
}

.web-bg-pattern {
  background-image: url("../../assets/images/web-ms-bg.svg");
  background-repeat: no-repeat;
  background-size: cover;
}

.waring-bg-pattern {
  background-image: url("../../assets/images/warning-pattern-bg.svg");
  background-repeat: no-repeat;
  background-size: cover;
}

.delete-bg-pattern {
  background-image: url("../../assets/images/permanent-delete.svg");
  background-repeat: no-repeat;
  background-size: cover;
}

.bg-mixed-pattern {
  background-image: url("../../assets/images/mixed-pattern.svg");
  background-repeat: no-repeat;
  background-size: cover;
}

.bg-watermark {
  background-image: url("../../assets/images/watermark-bg.svg");
  background-repeat: no-repeat;
  background-size: cover;
}

.mask {
  width: 32px;
  height: 32px;
  transform: translateY(30px);
  @extend .position-absolute, .top-5, .br-6, .align-center, .flex-column;
}

.prop-ms {
  .carousel-counter {
    bottom: 0px;
    right: 0px;
    border-radius: 6px 0px;
    font-size: 10px;
    @extend .z-index-1001,
    .position-absolute,
    .bg-black-50,
    .text-white,
    .py-4,
    .px-12;

    &.web {
      bottom: 10px;
      right: 10px;
      padding: 4px 16px !important;
      @extend .br-50px, .text-normal;
    }
  }

  .carousel-control-prev,
  .carousel-control-next {
    opacity: unset !important;
  }
}

.draggable-right {
  z-index: 10002;
  cursor: help;
  @extend .bg-blue-50,
  .position-absolute,
  .right-6,
  .bottom-80,
  .bg-accent-green,
  .br-25,
  .p-8;
}

.status-label-badge {
  @extend .flex-center, .py-6, .px-10, .text-normal;
  border-radius: 200px;
  max-width: fit-content;
}

.min-w-fit-content {
  min-width: fit-content;
}

.w-min-content {
  width: min-content !important;
}

.non-resizable {
  resize: none !important;
  -webkit-resize: none !important;
}

.selected-project {
  @extend .fw-600, .text-coal;
  position: relative;
}

// .selected-project:after {
//   position: absolute;
//   content: '';
//   width: 60%;
//   transform: translateX(-50%);
//   bottom: -2px;
//   left: 50%;
// }

.mirror-element {
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
}

.sticky-nav.is-sticky {
  @extend .bg-coal, .sticky-top, .w-710, .br-8, .mt-40, .p-12, .top-0;
  z-index: 1000;

  .property-details {
    display: none !important;
  }

  .prop-details {
    display: contents !important;
  }
}

.sticky.is-sticky {
  @extend .top-0, .sticky-top;

  .project-details {
    display: none !important;
  }

  .proj-details {
    display: contents !important;
  }
}

.drag-scroll-content {
  width: unset !important;
}

.user-select-none {
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.canvasjs-chart-credit {
  display: none;
}

.canvasjs-chart-tooltip div {
  padding: 8px !important;
  line-height: 20px !important;
}

.filter {
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
}

.filter-blur {
  filter: blur(10px);
}

.hover {

  .hover-div,
  .hover-footer {
    // transition: all 0.3s;
    visibility: hidden;
    border-left: 2px dashed $white;
    border-right: 2px dashed $white;
  }

  .hover-div {
    border-top: 2px dashed $white;
  }

  .hover-footer {
    border-bottom: 2px dashed $white;
  }

  &:hover {
    .hover-div {
      border-top: 2px dashed $dark-700;
      border-left: 2px dashed $dark-700;
      border-right: 2px dashed $dark-700;
      visibility: visible;
    }

    .hover-footer {
      border-bottom: 2px dashed $dark-700;
      border-left: 2px dashed $dark-700;
      border-right: 2px dashed $dark-700;
      visibility: visible;
    }
  }
}

.d-dropdown {
  .ng-value-container {
    .value {
      @extend .align-center-col, .header-5, .fw-semi-bold;
    }

    .count {
      @extend .text-sm, .fw-700, .position-absolute, .mt-12, .right-30;
    }
  }

  .ng-option-label {
    .value {
      @extend .align-center;
    }
  }
}

.unitArea {
  .activation {
    @extend .py-4,
    .flex-center,
    .fw-semi-bold,
    .flex-wrap,
    .cursor-pointer,
    .text-sm,
    .text-black-200;
  }

  .actived {
    font-weight: 700 !important;
    border-bottom: 1px solid $primary-black;
    color: $primary-black !important;
  }

  .active {
    font-weight: 800 !important;
    color: $primary-black !important;
  }
}

.white-box {
  .activation {
    @extend .px-20,
    .py-10,
    .bg-white,
    .mr-20,
    .flex-center,
    .fw-semi-bold,
    .flex-wrap,
    .br-4,
    .cursor-pointer;
  }

  .active {
    @extend .text-accent-green;
    font-weight: 700 !important;
    border-bottom: 2px solid $accent-green !important;
  }
}

.scroll-behaviour {
  overflow-x: auto;
  white-space: nowrap;
  scroll-behavior: smooth;
  /* Enable smooth scrolling behavior */
  transition: transform 0.3s ease;
  /* Apply smooth transition effect */
}

.pink-circle {
  width: 15px;
  height: 15px;
  border: 3px solid #a3eead;
  border-radius: 50%;
  position: absolute;
  top: 40%;
  left: 35%;
  animation: animationFramesOne 15s infinite linear;
}

.ellipse {
  position: absolute;
  width: 10px;
  height: 10px;
  top: 60%;
  left: 40%;
  border: 3px solid #22a39d;
  animation: animationFramesOne 15s infinite linear;
}

@keyframes animationFramesOne {
  0% {
    transform: translate(0px, 0px) rotate(0deg);
  }

  20% {
    transform: translate(75px, 0px) rotate(36deg);
  }

  40% {
    transform: translate(140px, 70px) rotate(72deg);
  }

  60% {
    transform: translate(85px, 125px) rotate(108deg);
  }

  80% {
    transform: translate(-40px, 70px) rotate(144deg);
  }

  100% {
    transform: translate(0px, 0px) rotate(0deg);
  }
}

.blue-dot {
  width: 14px;
  height: 14px;
  background: #8caabd;
  border-radius: 50%;
  position: absolute;
  top: 25%;
  right: 20%;
  animation: animationFramesTwo 13s infinite linear;
}

.rectangle {
  position: absolute;
  background: #ce0764;
  width: 10px;
  height: 10px;
  top: 25%;
  left: 50%;
  animation: animationFramesTwo 13s infinite linear;
}

.green-circle {
  position: absolute;
  width: 18px;
  height: 18px;
  border: 3px solid #fe8f8f;
  border-radius: 50%;
  bottom: 40%;
  left: 5%;
  animation: animationFramesTwo 15s infinite linear;
}

.rhombus {
  position: absolute;
  border: 3px solid #beff68;
  top: 10%;
  right: 30%;
  width: 12px;
  height: 12px;
  animation: animationFramesTwo 15s infinite linear;
}

@keyframes animationFramesTwo {
  0% {
    transform: translate(0px, 0px) rotate(0deg) scale(1);
  }

  20% {
    transform: translate(75px, 0px) rotate(36deg) scale(0.8);
  }

  40% {
    transform: translate(140px, 70px) rotate(72deg) scale(1);
  }

  60% {
    transform: translate(80px, 120px) rotate(108deg) scale(1.3);
  }

  80% {
    transform: translate(-40px, 70px) rotate(144deg) scale(1.2);
  }

  100% {
    transform: translate(0px, 0px) rotate(0deg) scale(1);
  }
}

.purple-dot {
  position: absolute;
  left: 25%;
  bottom: 20%;
  width: 350px;
  height: 350px;
  border-radius: 50%;
  animation: rotated 10s infinite linear;
}

.purple-dot:before {
  content: "";
  width: 10px;
  height: 10px;
  position: absolute;
  top: 50%;
  left: -5px;
  background: #ddabf6;
  border-radius: 50%;
}

.triangle {
  position: absolute;
  bottom: 25%;
  right: 10%;
  animation: rotated 20s infinite linear;
  width: 10px;
  height: 10px;
  background-color: #7fffd4;
}

@keyframes rotated {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.dot-mixed {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #f7e9ae;
  position: absolute;
  animation: animationFramesThree 8s linear 5s infinite alternate;
}

@keyframes animationFramesThree {
  0% {
    background-color: #c8dfdf;
    left: 20px;
    top: 20px;
  }

  25% {
    background-color: #dbc3b4;
    left: 200px;
    top: 20px;
  }

  50% {
    background-color: #a5e2b4;
    left: 200px;
    top: 200px;
  }

  75% {
    background-color: #eeb3b4;
    left: 20px;
    top: 200px;
  }

  100% {
    background-color: #ccd1d0;
    left: 20px;
    top: 20px;
  }
}

.shake {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #dbc3b4;
  animation: shake 10s infinite;
  transform: translate(-50%, -50%);
}

.polygon {
  animation: shake 10s infinite;
  transform: translate(-50%, -50%);
}

@keyframes shake {
  0% {
    transform: translate(1px, -15px) rotate(-1deg);
  }

  10% {
    transform: translate(-1px, 15px) rotate(-1deg);
  }

  20% {
    transform: translate(1px, 15px) rotate(0deg);
  }

  30% {
    transform: translate(30px, 15px) rotate(0deg);
  }

  40% {
    transform: translate(1px, -1px) rotate(1deg);
  }

  50% {
    transform: translate(-1px, -15px) rotate(-1deg);
  }

  60% {
    transform: translate(-30px, 1px) rotate(0deg);
  }

  70% {
    transform: translate(30px, 1px) rotate(-1deg);
  }

  80% {
    transform: translate(-1px, -1px) rotate(1deg);
  }

  90% {
    transform: translate(-30px, 0px) rotate(1deg);
  }

  100% {
    transform: translate(1px, 1px) rotate(0deg);
  }
}

.dot-accent-green {
  position: absolute;
  right: 356px;
  top: 170px;
  opacity: 0.7;
  animation: rotatedHalfTwo 15s alternate infinite linear;
  z-index: -1;
}

.dot-accent-green:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50px;
  width: 14px;
  height: 14px;
  background: #01fcf0;
  border-radius: 50%;
}

@keyframes rotatedHalfTwo {
  0% {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
  }

  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}

.bubble {
  border-radius: 50%;
  background-color: #ddabf6;
  height: 20px;
  position: absolute;
  width: 20px;
  animation: animationFramesFour 20s alternate infinite linear;
  right: 20%;
  top: 10%;
  transform: scale(0.3);
}

.bubble:after {
  content: "";
  left: 0px;
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  box-shadow: inset 0 20px 30px rgba(255, 255, 255, 0.3);
  background: -webkit-gradient(radial,
      center center,
      0px,
      center center,
      100%,
      color-stop(0%, rgba(255, 255, 255, 0.5)),
      color-stop(70%, rgba(255, 255, 255, 0)));

  background: -webkit-radial-gradient(center,
      ellipse cover,

      rgba(255, 255, 255, 0.5) 0%,

      rgba(255, 255, 255, 0) 70%);
}

.red-dot {
  position: absolute;
  top: 60%;
  right: 33%;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #b93434;
  animation: animationFramesFour 25s alternate infinite linear;
}

.octa {
  position: absolute;
  top: 50%;
  right: 20%;
  width: 10px;
  height: 10px;
  background-color: #024bd4;
  animation: animationFramesFour 25s alternate infinite linear;
}

@keyframes animationFramesFour {
  0% {
    transform: translate(160px, -80px);
  }

  100% {
    transform: translate(-150px, 210px);
  }
}

.w-fit-content {
  width: fit-content !important;
}

.ck-editor__editable_inline {
  min-height: 300px;
}

.custom-slider {
  accent-color: #3b9985;
  height: 5px;
}

.watermark-main-image {
  background-image: url("../../assets/images/preview-image.png");
  background-repeat: no-repeat;
  background-size: cover;
}

.left-top {
  top: 0;
  left: 0;
}

.left-center {
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}

.left-bottom {
  bottom: 0;
  left: 0;
}

.top-center {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

.center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.bottom-center {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.right-top {
  top: 0;
  right: 0;
}

.right-center {
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}

.right-bottom {
  bottom: 0;
  right: 0;
}

.conversation {
  position: relative;
  z-index: 0;
}

.conversation ::-webkit-scrollbar {
  transition: all 0.5s;
  width: 5px;
  height: 1px;
  z-index: 10;
}

.conversation .conversation-container:after {
  content: "";
  display: table;
  clear: both;
}

/* Messages */

.message {
  clear: both;
  position: relative;
  max-width: 60%;
  word-wrap: break-word;
}

.message.received {
  background: $purple-100;
  border-radius: 0px 5px 5px 5px;
  float: left;
}

.message.received-details {
  float: left;
}

.message.received:after {
  border-width: 0px 10px 10px 0;
  border-color: transparent $purple-100 transparent transparent;
  top: 0;
  left: -7px;
}

.message.sent {
  background: $accent-green;
  border-radius: 5px 0px 5px 5px;
  float: right;
}

.message.sent-details {
  float: right;
}

.message.sent:after {
  border-width: 0px 0 10px 10px;
  border-color: transparent transparent transparent $accent-green;
  top: 0;
  right: -7px;
}

.hovered-card {
  &:hover {
    @extend .box-shadow-40;

    .ic-hover {
      @extend .ic-black-100;
    }

    .hover-bg {
      background-color: $bg-secondary;
    }

    .typo-hover {
      @extend .text-black-100;
    }

    .heading-hover {
      @extend .fw-semi-bold, .fw-400;
    }

    .hover-container {
      display: block;
    }
  }

  .hover-container {
    display: none;
  }
}

#documentsupload {
  background-color: #f2f2f2;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

#documentsupload.ng-dirty.ng-valid,
#documentsupload.ng-touched.ng-valid,
#documentsupload.ng-touched:not(.ng-valid),
#documentsupload.ng-dirty:not(.ng-valid) {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

#documentsupload::placeholder {
  color: #999999;
  font-size: 10px;
}

.tag-card:hover .action-option {
  opacity: 1 !important;
  transition: 0.5s ease;
}

.padd-r.pr-36 {
  @extend .pr-36;
}

.app-tour-tooltip {
  position: absolute;
  top: 100%;
  /* Position below the parent element */
  left: 0;
  /* Align with the parent element */
  background: white;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  white-space: nowrap;
  /* Prevent text wrapping */
  height: 75px;
  width: 250px;
  pointer-events: auto;
}

.circle {
  transform: translate(-50%, -50%);
  border: 1px solid $dark-900;
}

.circle img {
  position: absolute;
  transform: translate(-50%, -50%);
}

.date-grid {
  display: grid;
  grid-template-columns: repeat(9, 1fr);
  gap: 8px;
  justify-content: center;
  text-align: center;
}

.date-item {
  transition: all 0.2s ease;

  &.selected {
    color: black;
    border-bottom: 2px solid #439B89;
    font-weight: bold;
  }

  &:hover {
    color: black;
    background-color: #DAF1EC;
  }
}

.overlay {
  background: rgba(0, 0, 0, 0.2) !important;
  backdrop-filter: blur(4px); // Optional blur effect
}

.marg-r-10 {
  margin-right: 10px !important;
}

.select-all-container {
  padding: 8px 10px;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  display: flex;
  align-items: center;
  position: relative;
  top: 0;
  background-color: #fff;
  z-index: 10;
  width: 100%;
  box-sizing: border-box;
  font-weight: bold;
}

.select-all-checkbox {
  margin-right: 8px;
  cursor: pointer;
  font-size: 14px;
  left: -4px;
  top: -4px;
}

.select-all-checkmark {
  border: 1px solid #ccc;
}

.select-all-checkmark.green-border {
  border: 1px solid #439b89 !important;
}

/* State classes */
.select-all-checkbox.checked {
  display: block;
}

.select-all-checkbox.unchecked {
  display: none;
}

.select-all-checkbox.indeterminate {
  display: block;
}

/* Opacity for empty state */
.select-all-checkbox.empty {
  opacity: 0.5;
}


.chart-wrapper {
  width: 100%;
  max-width: 100vw;
  margin: 0 auto;
  max-height: 500px;
  min-height: 500px;
  box-sizing: border-box;
}

#activityChart {
  width: 100% !important;
  height: 100% !important;
  display: block;
  max-width: 100%;
  max-height: 100%;
}
