import { Component, EventEmitter, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { filter, takeUntil } from 'rxjs';

import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { MarketingType, PropertyDateType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  changeCalendar,
  getAssignedToDetails,
  getPages,
  getTimeZoneDate,
  onFilterChanged,
  patchTimeZoneDate,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import {
  DeleteChannelPartner,
  FetchChannelPartner,
  UpdateChannelPartnerFiltersPayload,
} from 'src/app/reducers/manage-marketing/marketing.action';
import {
  getChannelPartnerList,
  getChannelPartnerListIsLoading,
  getChannelPartnerNameFiltersPayload,
  getIsCPBulkDeleteLoading,
} from 'src/app/reducers/manage-marketing/marketing.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  getUserBasicDetails,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { ChannelPartnerActionComponent } from './channel-partner-action/channel-partner-action/channel-partner-action.component';
import { ChannelPartnerAdvanceFilterComponent } from './channel-partner-advance-filter/channel-partner-advance-filter/channel-partner-advance-filter.component';

@Component({
  selector: 'channel-partners',
  templateUrl: './channel-partners.component.html',
})
export class ChannelPartnersComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  showEntriesSize: Array<number> = SHOW_ENTRIES;
  PageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  searchTerm: string;
  filtersPayload: any = {
    PageNumber: 1,
    PageSize: 10,
  };
  appliedFilter: any = {};
  showFilters: boolean = false;
  showLeftNav: boolean = true;
  private fetchCalled: boolean = false;

  gridApi: any;
  gridColumnApi: any;
  gridOptions: any;
  defaultColDef: any;
  columns: any[];
  defaultColumns: any[];
  isBulkDeleteLoading: boolean = false;

  allChannelParthnerData: any;
  @Input() allUserList: any;
  getPages = getPages;
  onFilterChanged = onFilterChanged;
  isChannelPartnerLoading: boolean = false;
  userList: any;
  userData: any;
  currentDate: Date = new Date();
  toDate: any = new Date();
  fromDate: any = new Date();
  canBulkDelete: boolean = false;

  constructor(
    private gridOptionsService: GridOptionsService,
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private shareDataService: ShareDataService,
    private modalService: BsModalService,
    private router: Router
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
  }

  ngOnInit(): void {
    this.selectedPageSize = 10;
    // this.store.dispatch(
    //   new FetchChannelPartner({
    //     path: 'v1/marketing/channelpartners',
    //     PageNumber: 1,
    //     PageSize: 10,
    //   })
    // );
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canBulkDelete = permissionsSet.has('Permissions.GlobalSettings.BulkDelete');
      });

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });

    this.store
      .select(getChannelPartnerListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.isChannelPartnerLoading = data;
      });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userList = data;
        this.allUserList = assignToSort(data, '');
      });

    this.store
      .select(getChannelPartnerNameFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = data;
        this.currOffset = this.filtersPayload?.PageNumber - 1;
        this.PageSize = this.filtersPayload?.PageSize;
        this.selectedPageSize = this.filtersPayload?.PageSize;
        this.appliedFilter = {
          ...this.appliedFilter,
          PageNumber: this.filtersPayload?.SearchText
            ? 1
            : this.filtersPayload?.PageNumber,
          PageSize: this.filtersPayload?.PageSize,
          SearchText: this.filtersPayload?.SearchText,
        };
      });
    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
    this.appliedFilter = {
      ...this.appliedFilter,
      AgencyNames: this.filtersPayload?.AgencyNames,
      PhoneNumber: this.filtersPayload?.PhoneNumber,
      EmailId: this.filtersPayload?.EmailId,
      Location: this.filtersPayload?.Location,
      AssociatedLeadsCount: this.filtersPayload?.AssociatedLeadsCount,
      AssociatedPospectsCount: this.filtersPayload?.AssociatedPospectsCount,
      AssociateProperty: this.filtersPayload?.AssociateProperty,
      Date: [
        patchTimeZoneDate(
          this.filtersPayload?.FromDate,
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
        patchTimeZoneDate(
          this.filtersPayload?.ToDate,
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
      ],
      DateType: PropertyDateType[this.filtersPayload?.DateType],
      ModifiedBy: this.filtersPayload?.ModifiedBy,
      PageSize: this.filtersPayload?.PageSize,
      PageNumber: this.filtersPayload?.PageNumber,
    };
    this.channelPartnerFilterFunction();
    this.store
      .select(getChannelPartnerList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allChannelParthnerData = data;
        if (data?.itemsCount === 0 && !this.fetchCalled) {
          this.store.dispatch(
            new FetchChannelPartner({
              ...this.filtersPayload,
              path: 'v1/marketing/channelpartners',
              PageNumber: 1,
            })
          );
          this.currOffset = 0;
          this.fetchCalled = true;
          setTimeout(() => {
            this.fetchCalled = false;
          }, 1000);
        }
      });
    this.cityGridSettings();

  }

  cityGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 60;
    this.gridOptions.columnDefs = [
      {
        headerName: 'Channel Partner Name',
        field: 'Channel Partner Name',
        pinned: window.innerWidth > 768 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned',
        // minWidth: 310,
        wrapText: true,
        autoHeight: true,
        suppressMovable: true,
        lockPosition: 'left',
        valueGetter: (params: any) => params.data?.firmName || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--';
          return `<p class="mt-20 text-truncate-1 break-all">${value}</p>`;
        },
      },
      {
        headerName: 'Phone Number',
        field: 'Phone Number',
        minWidth: 190,
        valueGetter: (params: any) => params.data?.contactNo || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--';
          return `<p>${value}</p>`;
        },
      },
      {
        headerName: 'Email',
        field: 'Email',
        minWidth: 190,
        valueGetter: (params: any) => params.data?.email || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--';
          return `<p>${value}</p>`;
        },
      },
      {
        headerName: 'Location',
        field: 'Location',
        minWidth: 190,
        valueGetter: (params: any) => [
          params.data?.address?.subLocality
            ? params.data?.address?.subLocality
            : params.data?.address?.locality,
          params.data?.address?.city,
          params.data?.address?.state,
          params.data?.address?.postalCode,
        ],
        cellRenderer: (params: any) => {
          if (
            params.data.address === null ||
            (params.value[0] === null &&
              params.value[1] === null &&
              params.value[2] === null &&
              params.value[3] === null)
          ) {
            return '--';
          }
          const fullAddress = `${params.value[0] ? params.value[0] : ''}${params.value[1] ? ', ' + params.value[1] : ''
            }${params.value[2] ? ', ' + params.value[2] : ''}${params.value[3] ? ', ' + params.value[3] : ''
            }`;
          return `<p class="text-truncate-1 break-all" title="${fullAddress}">
                    ${params.value[0] ? params.value[0] : ''}
                    ${params.value[1] ? ', ' + params.value[1] : ''}
                    ${params.value[2] ? ', ' + params.value[2] : ''}
                    ${params.value[3] ? ', ' + params.value[3] : ''}
                  </p>`;
        },
      },
      {
        headerName: 'Associated Leads',
        field: 'Associated Leads',
        hide: true,
        valueGetter: (params: any) => [params?.data?.leadsCount],
        cellRenderer: (params: any) => {
          const leadsCount = params?.value?.[0];
          const firmName = params?.data?.firmName;
          return `<p>
            ${leadsCount
              ? `<a class="cursor-pointer"   
                         data-firm-name="${firmName}"   
                         data-leads-count="${leadsCount}">  
                         ${leadsCount}  
                     </a>`
              : `<span>${leadsCount || '--'}</span>`
            }
          </p>`;
        },
        onCellClicked: (event: any) => {
          const clickedElement = event?.event?.target;
          if (clickedElement.tagName !== 'A') {
            return;
          }
          const firmName = event?.data?.firmName;
          const leadsCount = event?.data?.leadsCount;
          if (!firmName || !leadsCount) return;
          const isCtrlClick = event?.event?.ctrlKey;
          const navigateTo = () => {
            this.router.navigate(['leads', 'manage-leads'], {
              queryParams: {
                ChannelPartnerNames: JSON.stringify([firmName]),
                isNavigatedFromMarketing: true,
              },
            });
          };
          if (isCtrlClick) {
            window.open(
              `leads/manage-leads?isNavigatedFromMarketing=true&ChannelPartnerNames=${encodeURIComponent(
                JSON.stringify([firmName])
              )}`,
              '_blank'
            );
          } else {
            event.event.preventDefault();
            navigateTo();
          }
        },
        cellClass: (params: any) => (params?.data?.leadsCount ? '' : ''),
      },
      {
        headerName: 'Associated Data',
        field: 'Associated Data',
        hide: true,
        valueGetter: (params: any) => [params?.data?.prospectCount],
        cellRenderer: (params: any) => {
          const prospectCount = params?.value?.[0];
          const firmName = params?.data?.firmName;
          return `<p>
            ${prospectCount
              ? `<a class="cursor-pointer"   
                         data-firm-name="${firmName}"   
                         data-prospect-count="${prospectCount}">  
                         ${prospectCount}  
                     </a>`
              : `<span>${prospectCount || '--'}</span>`
            }
          </p>`;
        },
        onCellClicked: (event: any) => {
          const clickedElement = event?.event?.target;
          if (clickedElement.tagName !== 'A') {
            return;
          }
          const firmName = event?.data?.firmName;
          const prospectCount = event?.data?.prospectCount;
          if (!firmName || !prospectCount) return;
          const isCtrlClick = event?.event?.ctrlKey;
          const navigateTo = () => {
            this.router.navigate(['data', 'manage-data'], {
              queryParams: {
                ChannelPartnerNames: JSON.stringify([firmName]),
                isNavigatedFromMarketing: true,
              },
            });
          };
          if (isCtrlClick) {
            window.open(
              `data/manage-data?isNavigatedFromMarketing=true&ChannelPartnerNames=${encodeURIComponent(
                JSON.stringify([firmName])
              )}`,
              '_blank'
            );
          } else {
            event.event.preventDefault();
            navigateTo();
          }
        },
        cellClass: (params: any) => (params?.data?.prospectCount ? '' : ''),
      },
      // {
      //   hide: true,
      //   headerName: 'Associate Property',
      //   field: 'Associate Property',
      //   valueGetter: (params: any) => params.data?.name || '--',
      //   cellRenderer: (params: any) => {
      //     const value = params.value || '--';
      //     return `<p>${value}</p>`;
      //   },
      // },
      {
        hide: true,
        minWidth: 195,
        headerName: 'Created',
        field: 'Created',
        valueGetter: (params: any) => [
          getAssignedToDetails(params.data.createdBy, this.userList, true) ||
          '',
          params.data?.createdOn
            ? getTimeZoneDate(
              params.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'dayMonthYear'
            ) +
            ' at ' +
            getTimeZoneDate(
              params.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'timeWithMeridiem'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4">${params.value[0]}</p>
          <p class='fw-400'>${params.value[1]}</p>
                      <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.baseUTcOffset &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' +
              this.userData?.timeZoneInfo?.timeZoneName +
              ')'
              : ''
            }</p>`;
        },
      },
      {
        hide: true,
        minWidth: 195,
        headerName: 'Modified',
        field: 'Modified',
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params.data.lastModifiedBy,
            this.userList,
            true
          ) || '',
          params.data?.lastModifiedOn
            ? getTimeZoneDate(
              params.data?.lastModifiedOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'dayMonthYear'
            ) +
            ' at ' +
            getTimeZoneDate(
              params.data?.lastModifiedOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'timeWithMeridiem'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4">${params.value[0]}</p>
          <p class='fw-400'>${params.value[1]}</p>
                      <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' +
              this.userData?.timeZoneInfo?.timeZoneName +
              ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'RERA Number',
        field: 'RERA Number',
        minWidth: 190,
        hide: true,
        valueGetter: (params: any) => params.data?.reraNumber || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--';
          return `<p>${value}</p>`;
        },
      },
      {
        headerName: 'Company Name',
        field: 'Company Name',
        minWidth: 190,
        hide: true,
        valueGetter: (params: any) => params.data?.companyName || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--';
          return `<p>${value}</p>`;
        },
      },
      {
        headerName: 'Actions',
        minWidth: 110,
        maxWidth: 110,
        filter: false,
        cellRenderer: ChannelPartnerActionComponent,
      },
    ];
    if (this.canBulkDelete) {
      this.gridOptions.columnDefs.unshift({
        showRowGroup: true,
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        filter: false,
        pinned: window.innerWidth > 768 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned mt-8',
        maxWidth: 50,
        suppressMovable: true,
        lockPosition: 'left',
      })
    }
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params?.api;
    this.gridColumnApi = params.columnApi;
    this.toggleColumns(params);
  }

  assignCount() {
    this.PageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      PageSize: this.PageSize,
      PageNumber: 1,
    };
    this.store.dispatch(
      new UpdateChannelPartnerFiltersPayload(this.filtersPayload)
    );
    this.store.dispatch(new FetchChannelPartner(this.filtersPayload));
    this.currOffset = 0;
  }

  hiddenKeys: string[] = ['PageSize', 'PageNumber'];
  hasValue(value: any): boolean {
    if (value === null || value === undefined) {
      return false;
    }
    const stringValue = String(value);
    return stringValue.trim().length > 0;
  }
  isKeyVisible(key: string): boolean {
    return !this.hiddenKeys.includes(key);
  }

  getArrayOfFilters(key: string, values: string) {
    if (
      ['PageSize', 'PageNumber', 'SearchText'].includes(key) ||
      values?.length === 0
    )
      return [];
    else if (key === 'Date' && Array.isArray(values) && values.length === 2) {
      if (key === 'Date' && values[0] !== null) {
        this.toDate = setTimeZoneDate(
          new Date(values[0]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.fromDate = setTimeZoneDate(
          new Date(values[1]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        const formattedToDate = getTimeZoneDate(
          this.toDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const formattedFromDate = getTimeZoneDate(
          this.fromDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
        return [dateRangeString];
      } else {
        return null;
      }
    }
    return values?.toString()?.split(',');
  }
  onRemoveFilter(key: string, value: any) {
    if (
      typeof this.appliedFilter[key] === 'string' ||
      typeof this.appliedFilter[key] === 'number' ||
      typeof this.appliedFilter[key] === 'boolean'
    ) {
      this.appliedFilter[key] = null;
    } else if (['DateType', 'Date'].includes(key)) {
      this.appliedFilter[key] = null;
    } else if (Array.isArray(this.appliedFilter[key])) {
      const valueToRemove = value.toString().trim().toLowerCase();
      this.appliedFilter[key] = this.appliedFilter[key]?.filter((item: any) => {
        return item.toString().trim().toLowerCase() !== valueToRemove;
      });
    }
    this.channelPartnerFilterFunction();
  }

  channelPartnerFilterFunction() {
    if (
      this.appliedFilter?.Location?.length ||
      this.appliedFilter?.AssociatedLeadsCount ||
      this.appliedFilter?.AssociatedPospectsCount ||
      this.appliedFilter?.AssociateProperty ||
      this.appliedFilter?.CreatedBy ||
      this.appliedFilter?.DateType ||
      this.appliedFilter?.FromDate ||
      this.appliedFilter?.Date?.[0] ||
      this.appliedFilter?.ModifiedBy
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }

    let leadsCounts = this.appliedFilter?.AssociatedLeadsCount?.split('-');
    let prospectsCounts =
      this.appliedFilter?.AssociatedPospectsCount?.split('-');

    let LeadsMinCount =
      this.appliedFilter?.AssociatedLeadsCount === 'Above 100'
        ? 100
        : leadsCounts
          ? leadsCounts[0]
          : undefined;
    let LeadsMaxCount =
      this.appliedFilter?.AssociatedLeadsCount === 'Above 100'
        ? null
        : leadsCounts
          ? leadsCounts[1]
          : undefined;

    let ProspectMinCount =
      this.appliedFilter?.AssociatedPospectsCount === 'Above 100'
        ? 100
        : prospectsCounts
          ? prospectsCounts[0]
          : undefined;
    let ProspectMaxCount =
      this.appliedFilter?.AssociatedPospectsCount === 'Above 100'
        ? null
        : prospectsCounts
          ? prospectsCounts[1]
          : undefined;

    this.filtersPayload = {
      ...this.filtersPayload,
      path: 'v1/marketing/channelpartners',
      PageNumber: this.appliedFilter?.PageNumber,
      PageSize: this.appliedFilter?.PageSize,
      SearchText: this.searchTerm,
      AgencyNames: this.appliedFilter?.AgencyNames,
      PhoneNumber: this.appliedFilter?.PhoneNumber,
      EmailId: this.appliedFilter?.EmailId,
      Location: this.appliedFilter?.Location,
      LeadsMinCount,
      LeadsMaxCount,
      ProspectMinCount,
      ProspectMaxCount,
      AssociatedLeadsCount: this.appliedFilter?.AssociatedLeadsCount,
      AssociatedPospectsCount: this.appliedFilter?.AssociatedPospectsCount,
      AssociateProperty: this.appliedFilter?.AssociateProperty,
      DateType: PropertyDateType[this.appliedFilter?.DateType],
      CreatedBy: this.appliedFilter?.CreatedBy,
      FromDate: setTimeZoneDate(
        this.appliedFilter?.Date?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      ToDate: setTimeZoneDate(
        this.appliedFilter.Date?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      ModifiedBy: this.appliedFilter?.ModifiedBy,
    };

    this.store.dispatch(
      new UpdateChannelPartnerFiltersPayload(this.filtersPayload)
    );
    this.store.dispatch(new FetchChannelPartner(this.filtersPayload));

    this.currOffset = 0;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      PageSize: this.PageSize,
      PageNumber: e + 1,
    };
    this.store.dispatch(
      new UpdateChannelPartnerFiltersPayload(this.filtersPayload)
    );
    this.store.dispatch(
      new FetchChannelPartner({
        path: 'v1/marketing/channelpartners',
        ...this.filtersPayload,
        PageNumber: e + 1,
      })
    );
  }

  exportChannelPartner() {
    let { PageNumber, PageSize, ...filteredPayload } = this.filtersPayload;
    let initialState: any = {
      payload: {
        ...filteredPayload,
        exportType: MarketingType.ChannelPartner,
        path: 'channelpartner',
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };

    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  deselectOptions() {
    let selectedNodes = this.gridApi?.getSelectedNodes();
    selectedNodes.forEach((node: any) => node.setSelected(false));
  }

  deleteChannelPartner() {
    let initialState: any = {
      type: 'manageMarketingDelete',
      data: {
        buttonContent: 'Delete',
        fieldType: 'Delete',
        heading: `Deleting Channel Partners?`,
        bulkMessage: `Are you sure you want to delete the <b>"${this.gridApi?.getSelectedNodes()?.length
          }"</b> selected channel partners?`,
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.isClickYesOrNo(true);
        }
      });
    }
  }

  isClickYesOrNo(isClick: boolean) {
    this.isBulkDeleteLoading = true;
    let selectedNodes = this.gridApi?.getSelectedNodes();
    let selectedIds = selectedNodes?.map((node: any) => node.data?.id);
    this.store.dispatch(new DeleteChannelPartner(selectedIds));
    const loadingSubscription = this.store
      .select(getIsCPBulkDeleteLoading)
      .pipe(filter((isLoading: boolean) => !isLoading))
      .subscribe(() => {
        this.isBulkDeleteLoading = false;
        this.modalService.hide();
        this.store.dispatch(
          new UpdateChannelPartnerFiltersPayload(this.filtersPayload)
        );
        this.store.dispatch(new FetchChannelPartner(this.filtersPayload));
      });
  }

  openBulkDeleteModal(bulkDeleteModal: any) {
    this.modalRef = this.modalService.show(bulkDeleteModal, {
      class: 'right-modal modal-500',
    });
  }

  openConfirmDeleteModal(agencyName: string, agencyId: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: agencyName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeLead(agencyId);
        }
      });
    }
  }

  removeLead(id: string): void {
    const selectedNodes = this.gridApi?.getSelectedNodes();
    const selectedNode = selectedNodes?.find(
      (lead: any) => lead?.data?.id === id
    );
    if (selectedNode) {
      this.gridApi?.deselectNode(selectedNode);
    }
    const remainingSelectedNodes = this.gridApi?.getSelectedNodes();
    if (!remainingSelectedNodes || remainingSelectedNodes.length === 0) {
      this.modalService.hide();
    }
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (this.searchTerm === '' || this.searchTerm === null) {
        return;
      }
      this.channelPartnerFilterFunction();
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTerm = null;
      this.channelPartnerFilterFunction();
    }
  }

  openAdvFiltersModal() {
    let initialState: any = {
      class: 'modal-600 modal-dialog tb-modal-unset',
      initialState: {
        applyAdvancedFilter: this.applyAdvancedFilter.bind(this),
        onClearAllFilters: this.onClearAllFilters.bind(this),
        appliedFilter: this.appliedFilter,
      },
    };
    const modalRef = this.modalService.show(
      ChannelPartnerAdvanceFilterComponent,
      initialState
    );
  }

  toggleColumns(params: any): void {
    this.columns = params?.columnApi?.getColumns()?.map((column: any) => {
      return {
        label: column?.userProvidedColDef?.headerName,
        value: column,
      };
    });
    this.columns = this.columns
      .slice(2, this.columns?.length - 1)
      .sort((a: any, b: any) => a?.label?.localeCompare(b?.label));
    this.defaultColumns = this.columns?.filter(
      (col) => col?.value?.getColDef()?.hide !== true
    );

    let columnState = JSON.parse(
      localStorage.getItem('myChannelPartnerColumnState')
    );
    if (columnState) {
      this.gridColumnApi.applyColumnState({
        state: columnState,
        applyOrder: true,
      });
    }

    let columnData = localStorage
      .getItem('manage-channel-partner-columns')
      ?.split(',');

    if (columnData?.length) {
      let visibleColumns = this.columns?.filter((col: any) =>
        columnData?.includes(col.label)
      );
      this.defaultColumns = visibleColumns;
      this.onColumnsSelected(visibleColumns);
    }
  }

  onColumnsSelected(columns: any[]) {
    let colData = columns?.map((column: any) => column.label);
    localStorage.setItem('manage-channel-partner-columns', colData?.toString());
    if (!columns) {
      columns = this.defaultColumns;
    }
    const cols = columns?.map((col) => col.value);
    this.gridColumnApi?.setColumnsVisible(cols, true);
    const nonSelectedCols = this.columns?.filter((col: any) => {
      return !cols.includes(col.value);
    });
    this.gridColumnApi?.setColumnsVisible(
      nonSelectedCols.map((col) => col.value),
      false
    );
    var columnState: any = this.gridColumnApi.getColumnState();
    localStorage.setItem(
      'myChannelPartnerColumnState',
      JSON.stringify(
        columnState.map((column: any) => ({
          ...column,
          sort: null,
        }))
      )
    );
    this.gridColumnApi.applyColumnState({
      state: columnState,
      applyOrder: true,
    });
  }

  onSetColumnDefault() {
    this.defaultColumns = this.columns.filter(
      (col) => col.value.getColDef().hide !== true
    );
    this.onColumnsSelected(this.defaultColumns);
  }

  onClearAllFilters(data: string) {
    if (this.appliedFilter && typeof this.appliedFilter === 'object') {
      Object.keys(this.appliedFilter).forEach((key) => {
        if (key !== 'PageNumber' && key !== 'PageSize') {
          this.appliedFilter[key] = null;
        }
      });
    }
    this.channelPartnerFilterFunction();
  }

  applyAdvancedFilter() {
    this.channelPartnerFilterFunction();
    this.modalService.hide();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
