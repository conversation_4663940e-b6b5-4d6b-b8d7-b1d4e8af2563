<div class="lead-adv-filter px-30 bg-white brbl-15 brbr-15" [formGroup]="filtersForm">
    <div class="h-100-100 scrollbar position-relative">
        <div class="adv-filter">
            <div class="flex-between ip-flex-start ip-flex-col ip-col-reverse">
                <div class="flex-column">
                    <div class="field-label">{{'LEADS.filter-by' | translate}}</div>
                    <ul class="d-flex flex-wrap">
                        <li *ngFor="let visibility of leadVisibility">
                            <div name="leadVisibility"
                                (click)="filtersForm.get('leadVisibility')?.setValue(visibility?.value); trackerFeatures(visibility.displayName)"
                                class="px-20 py-6 br-4 mr-8 mb-4 ip-mb-10 align-center cursor-pointer"
                                id="clkLeadsCurrent{{visibility}}" data-automate-id="clkLeadsCurrent{{visibility}}"
                                [ngClass]="getFormValue('leadVisibility') === visibility?.value ? 'bg-black-200 text-white border-black-200' : 'btn-transparent'">
                                {{ visibility?.displayName}}</div>
                        </li>
                    </ul>
                </div>
                <div class="filters-grid d-flex pl-0">
                    <div class="dropdown-date-picker d-flex rounded">
                        <div class="bg-white rounded-start manage-select datefilter-scroll">
                            <ng-select [virtualScroll]="true" placeholder="{{'GLOBAL.all'| translate}}"
                                [searchable]="false" [ngModelOptions]="{standalone: true}" ResizableDropdown
                                class="lead-date ip-max-w-80px min-w-60" [(ngModel)]="dateType" (change)="dateChange()">
                                <ng-option name="dateType" ngDefaultControl *ngFor="let dType of dateTypeList"
                                    [value]="dType">{{dType}}</ng-option>
                            </ng-select>
                        </div>
                        <div class="date-picker align-center py-4 rounded-end" id="leadsAppointmentDate"
                            data-automate-id="leadsAppointmentDate">
                            <span class="ic-appointment icon ic-xxs ic-black" [owlDateTimeTrigger]="dt1"></span>
                            <input type="text" readonly [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1" [max]="maxDate"
                                [selectMode]="'range'" class="pl-20 ph-pl-12 text-large ph-w-150px"
                                placeholder="ex. 5-03-2025 - 14-03-2025"
                                (ngModelChange)="filterDate = $event; dateChange()"
                                [ngModelOptions]="{standalone: true}" [ngModel]="filterDate" />
                            <owl-date-time [pickerType]="'calendar'" #dt1
                                (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                        </div>
                    </div>
                    <div *ngIf="filterDate[0]" class="bg-coal align-center cursor-pointer px-8 br-6 ml-10"
                        (click)="onResetDateFilter();trackingService.trackFeature('Web.Leads.Filter.Reset.Click')">
                        <span class="ic-refresh ic-white"></span>
                    </div>
                </div>
            </div>
            <div class="field-label" *ngIf="leadTags?.length > 0">{{'LEADS.tags' | translate}}</div>
            <ul class="d-flex flex-wrap">
                <ng-container *ngFor="let leadTag of leadTags">
                    <div class="align-center br-4 cursor-pointer px-12 py-6 mr-8 mb-4 border"
                        [ngClass]="{'border-black': getFormValue('CustomFlags') && getFormValue('CustomFlags')?.includes(leadTag.name) }"
                        (click)="filterTag(leadTag.name)">
                        <div class="mr-4">
                            <img [type]="'leadrat'"
                                [appImage]="getFormValue('CustomFlags') && getFormValue('CustomFlags')?.includes(leadTag.name) ? leadTag?.activeImagePath : leadTag?.inactiveImagePath"
                                class="w-16 h-16">
                        </div>
                        <span
                            [ngClass]="{ 'opacity-5': !(getFormValue('CustomFlags') && getFormValue('CustomFlags')?.includes(leadTag.name))}">{{leadTag?.name}}</span>
                    </div>
                </ng-container>
            </ul>
            <div class="field-label" *ngIf="currentPath !== '/invoice'">{{'LEAD_FORM.meeting'| translate}}/
                {{globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Status' : 'Site Visit Status'}}</div>
            <ul *ngIf="currentPath !== '/invoice'" class="d-flex flex-wrap">
                <li *ngFor="let meeting of doneStatusList">
                    <div name="meeting"
                        [ngClass]="getFormValue('MeetingOrVisitStatuses')?.includes(meeting) ? 'bg-black-200 text-white border-black-200' : 'btn-transparent'"
                        class="px-20 py-8 br-4 mr-8 mb-4 ip-mb-10 align-center cursor-pointer"
                        id="clkLeadsCurrent{{meeting}}" data-automate-id="clkLeadsCurrent{{meeting}}"
                        (click)="OnDoneStatus(meeting)">
                        {{
                        globalSettingsData?.shouldRenameSiteVisitColumn
                        ? (meeting === 'Site Visit Done'
                        ? 'Referral Taken'
                        : meeting === 'Site Visit Not Done'
                        ? 'Referral Not Taken'
                        : meeting)
                        : meeting
                        }}
                    </div>
                </li>
                <ng-container
                    *ngIf="getFormValue('MeetingOrVisitStatuses') && getFormValue('MeetingOrVisitStatuses')?.length">
                    <div class="filters-grid py-0 ip-pl-0 mb-4">
                        <div class="dropdown-date-picker d-flex rounded position-relative">
                            <div class="date-picker align-center w-200 rounded py-6 border-start-0"
                                id="leadsAppointmentDate" data-automate-id="leadsAppointmentDate">
                                <span class="ic-appointment icon ic-xxs ic-black" [owlDateTimeTrigger]="dt2"></span>
                                <input [max]="maxDate" type="text" readonly [owlDateTimeTrigger]="dt2"
                                    [owlDateTime]="dt2" [selectMode]="'range'" class="font-weight-400 pl-24 text-large"
                                    placeholder="ex. 19-06-2025 - 29-06-2025"
                                    (ngModelChange)="DateForMeetingOrVisit = $event; MeetingOrVisitdateChange()"
                                    [ngModelOptions]="{standalone: true}" [ngModel]="DateForMeetingOrVisit" />
                                <owl-date-time [pickerType]="'calendar'" #dt2
                                    (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                            </div>
                            <div class="position-absolute top-5 right-5 cursor-pointer"
                                *ngIf="getFormValue('FromDateForMeetingOrVisit')"
                                (click)="filtersForm.patchValue({FromDateForMeetingOrVisit:null,ToDateForMeetingOrVisit:null});DateForMeetingOrVisit=[]">
                                <span class="icon ic-refresh ic-coal ic-xs"></span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="flex-column ng-select-sm">
                            <div class="mr-20 w-250 position-relative">
                                <ng-select [clearSearchOnAdd]="true"
                                    [items]="canViewAllUsers ? allUsersList : reportees"
                                    [ngClass]="{ 'pe-none blinking': isReporteesIsLoading || isUsersListForReassignmentIsLoading }"
                                    [multiple]="true" [closeOnSelect]="false" placeholder="Select Users"
                                    ResizableDropdown bindLabel="fullName" bindValue="id"
                                    formControlName="AppointmentDoneByUserIds">
                                    <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                        <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                        <span class="ng-value-label"> {{item.firstName + ' ' +
                                            item.lastName}}</span>
                                    </ng-template>
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="flex-between">
                                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                    class="checkmark"></span><span class="text-truncate-1 break-all">
                                                    {{item.firstName}} {{item.lastName}}</span></div>
                                            <span class="text-disabled" *ngIf="!item.isActive">( Disabled )</span>
                                        </div>
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>
                    </div>
                </ng-container>
            </ul>
        </div>
        <div class="d-flex flex-wrap ng-select-sm">
        </div>
        <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
            <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Assign:</legend>
            <div class="d-flex w-100 flex-wrap ng-select-sm">
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="justify-between align-end mr-20">
                        <div class="field-label">{{globalSettingsData?.isDualOwnershipEnabled ? 'Primary owner' :
                            'Assign To'}}</div>
                        <div class="d-flex flex-wrap align-end">
                            <label class="checkbox-container mb-4 ml-4">
                                <input type="checkbox" formControlName="IsWithHistory"
                                    (change)="toggleHistoryTeam('history')">
                                <span class="checkmark"></span>History
                            </label>
                            <label class="checkbox-container mb-4 ml-4">
                                <input type="checkbox" formControlName="IsWithTeam"
                                    (change)="toggleHistoryTeam('team')">
                                <span class="checkmark"></span>{{'DASHBOARD.with-team' | translate}}
                            </label>
                        </div>

                    </div>
                    <div class="mr-20 position-relative">
                        <ng-select [virtualScroll]="true" [items]="canViewAllUsers ? allUsersList : reportees"
                            [ngClass]="{ 'pe-none blinking': isReporteesIsLoading || isUsersListForReassignmentIsLoading }"
                            ResizableDropdown [multiple]="true" [closeOnSelect]="false"
                            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName" bindValue="id"
                            formControlName="assignTo">
                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="flex-between">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.firstName}}
                                            {{item.lastName}} </span></div><span class="text-disabled"
                                        *ngIf="!item.isActive">( Disabled
                                        )</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100" *ngIf="globalSettingsData?.isDualOwnershipEnabled">
                    <div class="field-label">Secondary owner</div>
                    <div class="mr-20">
                        <ng-select *ngIf="!reporteesIsLoading else fieldLoader" [clearSearchOnAdd]="true"
                            formControlName="SecondaryUsers" [items]="allUsersList"
                            [ngClass]="{ 'pe-none blinking': isUsersListForReassignmentIsLoading }" ResizableDropdown
                            [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                            bindLabel="fullName" bindValue="id">
                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="flex-between">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.firstName}}
                                            {{item.lastName}}</span> </div> <span class="text-disabled"
                                        *ngIf="!item.isActive">( Disabled
                                        )</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100" *ngIf="globalSettingsData?.isDualOwnershipEnabled && showOwnerType">
                    <div class="field-label">Owner Type</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="ownerSelection" [closeOnSelect]="true"
                            ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" bindLabel="dispName"
                            bindValue="value" formControlName="ownerSelection">
                            <ng-option name="ownerType" *ngFor="let owner of ownerSelection"
                                [value]="owner?.value">{{owner?.dispName}}</ng-option>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{globalSettingsData?.isDualOwnershipEnabled ? 'Primary Assigned From' :
                        'Assigned From'}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="allUsersList"
                            [ngClass]="{ 'pe-none blinking': isUsersListForReassignmentIsLoading }" ResizableDropdown
                            [multiple]="true" formControlName="AssignedFromIds" [closeOnSelect]="false"
                            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName" bindValue="id">
                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="flex-between">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span class="text-truncate-1 break-all">
                                            {{item.firstName}}
                                            {{item.lastName}} </span></div><span class="text-disabled"
                                        *ngIf="!item.isActive">( Disabled
                                        )</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100" *ngIf="globalSettingsData?.isDualOwnershipEnabled">
                    <div class="field-label">Secondary Assigned From</div>
                    <div class="mr-20">
                        <ng-select [clearSearchOnAdd]="true" [items]="allUsersList"
                            [ngClass]="{ 'pe-none blinking': isUsersListForReassignmentIsLoading }" ResizableDropdown
                            [multiple]="true" formControlName="SecondaryFromIds" [closeOnSelect]="false"
                            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName" bindValue="id">
                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="flex-between">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.firstName}}
                                            {{item.lastName}}</span> </div> <span class="text-disabled"
                                        *ngIf="!item.isActive">( Disabled
                                        )</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Assignment Done By</div>
                    <div class="mr-20">
                        <ng-select [clearSearchOnAdd]="true" [items]="allUsersList"
                            [ngClass]="{ 'pe-none blinking': isUsersListForReassignmentIsLoading }" ResizableDropdown
                            [multiple]="true" formControlName="DoneBy" [closeOnSelect]="false"
                            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName" bindValue="id">
                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="flex-between">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.firstName}}
                                            {{item.lastName}}</span> </div> <span class="text-disabled"
                                        *ngIf="!item.isActive">( Disabled
                                        )</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Original Owner</div>
                    <div class="mr-20">
                        <ng-select [clearSearchOnAdd]="true" [items]="allUsersList"
                            [ngClass]="{ 'pe-none blinking': isUsersListForReassignmentIsLoading }" ResizableDropdown
                            [multiple]="true" formControlName="OriginalOwner" [closeOnSelect]="false"
                            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName" bindValue="id">
                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="flex-between">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.firstName}}
                                            {{item.lastName}}</span> </div> <span class="text-disabled"
                                        *ngIf="!item.isActive">( Disabled
                                        )</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
            <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Status & Source:</legend>
            <div class="d-flex w-100 flex-wrap ng-select-sm">
                <div *ngIf="currentPath!=='/invoice'" class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'GLOBAL.status' | translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true"
                            [items]="isCustomStatusEnabled ? customStatusList : masterLeadStatus" ResizableDropdown
                            [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                            bindLabel="displayName" bindValue="id" formControlName="StatusIds"
                            (change)="onStatusChange()"
                            [ngClass]="{ 'pe-none blinking': isCustomStatusEnabled && isCustomStatusListIsLoading }">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected">
                                    <span class="checkmark"></span><span class="text-truncate-1 break-all">
                                        {{item.displayName}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div *ngIf="currentPath!=='/invoice'" class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'LEADS.sub-status' | translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" formControlName="SubStatusIds" [items]="subStatusList"
                            [multiple]="true" [closeOnSelect]="false" ResizableDropdown
                            [ngClass]="{ 'pe-none blinking': isCustomStatusEnabled && isCustomStatusListIsLoading }"
                            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName" bindValue="id">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span
                                        class="text-truncate-1 break-all">{{item.displayName}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100" *ngIf="permission?.has('Permissions.Leads.ViewLeadSource')">
                    <div class="field-label">{{'LEADS.source' | translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="leadSources" [multiple]="true"
                            bindLabel="displayName" bindValue="displayName"
                            [ngClass]="{'blinking pe-none': isSourcesLoading  || isLeadSourceListLoading}"
                            [closeOnSelect]="false" formControlName="Source" ResizableDropdown
                            placeholder="{{'GLOBAL.select' | translate}}" (change)="updateSubSource()">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span
                                        class="text-truncate-1 break-all">{{item?.displayName}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100" *ngIf="permission?.has('Permissions.Leads.ViewLeadSource')">
                    <div class="field-label">{{'LEADS.sub-source' | translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="subSourceList"
                            [ngClass]="{ 'pe-none blinking': isSubSourceListIsLoading }" ResizableDropdown
                            [multiple]="true" formControlName="SubSources" [closeOnSelect]="false"
                            placeholder="{{'GLOBAL.select' | translate}}">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container" title="{{item}}"><input type="checkbox"
                                        id="item-{{index}}" data-automate-id="item-{{index}}"
                                        [checked]="item$.selected"><span class="checkmark"></span><span
                                        class="text-truncate-1 break-all">{{item}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
            <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Project & Property:</legend>
            <div class="d-flex w-100 flex-wrap ng-select-sm">
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'SIDEBAR.project'| translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="projectList"
                            [ngClass]="{ 'pe-none blinking': isProjectListLoading }" ResizableDropdown [multiple]="true"
                            [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="name"
                            bindValue="name" formControlName="Projects">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="flex-between">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.name}}</span></div>
                                    <span class="text-disabled" *ngIf="item.isArchived">( Deleted )</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'LABEL.property'| translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="propertyList"
                            [ngClass]="{ 'pe-none blinking': isPropertyListIsLoading }" ResizableDropdown
                            [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                            bindLabel="title" bindValue="title" formControlName="Properties">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="flex-between">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.title}}</span></div>
                                    <span class="text-disabled" *ngIf="item.isArchived">( Deleted )</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'LABEL.property'| translate}} {{'LABEL.type'| translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="propertyType" [multiple]="true"
                            [closeOnSelect]="false" formControlName="PropertyType" ResizableDropdown
                            placeholder="{{'GLOBAL.select' | translate}}" bindValue="id" bindLabel="displayName"
                            (change)="updatePropertyType()">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span
                                        class="text-truncate-1 break-all">{{item?.displayName}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'LABEL.property'| translate}} {{'LABEL.sub-type'| translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="propertySubTypes" [multiple]="true"
                            formControlName="PropertySubType" [closeOnSelect]="false" ResizableDropdown
                            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="displayName" bindValue="id">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span
                                        class="text-truncate-1 break-all">{{item?.displayName}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <ng-container *ngIf="!globalSettingsData?.isCustomLeadFormEnabled">
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">{{'PROPERTY.bhk'| translate}}</div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="noOfBhk" [multiple]="true"
                                [closeOnSelect]="false" formControlName="NoOfBHKs" ResizableDropdown
                                placeholder="{{'GLOBAL.select' | translate}}">
                                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                    <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                    <span class="ng-value-label">{{getBHKDisplayString(item)}}</span>
                                </ng-template>
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span>{{getBHKDisplayString(item)}}
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">{{'PROPERTY.bhk' | translate}} {{'LABEL.type'| translate}}</div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="bhkType" [multiple]="true"
                                formControlName="BHKTypes" [closeOnSelect]="false" ResizableDropdown
                                placeholder="{{'GLOBAL.select' | translate}}">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                </ng-container>
                <ng-container *ngIf="globalSettingsData?.isCustomLeadFormEnabled">
                    <!-- <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">BR</div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="numbers10" [multiple]="true"
                                [closeOnSelect]="false" ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}"
                                bindLabel="display" formControlName="NoOfBHKs" bindValue="value">
                                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                    <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                    <span class="ng-value-label">{{getBRDisplayString(item?.display)}}</span>
                                </ng-template>
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span>{{getBRDisplayString(item?.display)}}
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div> -->
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Baths</div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="numbers10" [multiple]="true"
                                [closeOnSelect]="false" ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}"
                                bindLabel="display" formControlName="Baths" bindValue="value">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span>{{item?.display}}
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Beds</div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="numbers" [multiple]="true"
                                [closeOnSelect]="false" ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}"
                                bindLabel="display" formControlName="Beds" bindValue="value">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span>{{item?.display}}
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Furnish Status</div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="furnishStatus" [multiple]="true"
                                [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="value"
                                bindValue="dispName" ResizableDropdown formControlName="Furnished">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.dispName}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Preferred Floor</div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="floorOptions" [multiple]="true"
                                formControlName="Floors" [closeOnSelect]="false"
                                placeholder="{{'GLOBAL.select' | translate}}" ResizableDropdown>
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Offering Type</div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="offerType" [multiple]="true"
                                [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                                bindLabel="displayName" formControlName="OfferTypes" bindValue="displayName"
                                ResizableDropdown>
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.displayName}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                </ng-container>
                <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Possession</div>
                    <div class="mr-20">
                        <app-possession-filter #possessionFilter
                            [initialPossessionType]="filtersForm.get('PossesionType')?.value"
                            [initialFromPossessionDate]="filtersForm.get('FromPossesionDate')?.value"
                            [initialToPossessionDate]="filtersForm.get('ToPossesionDate')?.value"
                            [userTimeZoneOffset]="userData?.timeZoneInfo?.baseUTcOffset"
                            (possessionFilterChange)="onPossessionFilterChange($event)">
                        </app-possession-filter>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
            <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Location:</legend>
            <div class="d-flex w-100 flex-wrap ng-select-sm">
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'LOCATION.location'| translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="locations"
                            [ngClass]="{ 'pe-none blinking': isLocationListLoading }" ResizableDropdown
                            [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                            formControlName="Locations">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Locality</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="localites"
                            [ngClass]="{ 'pe-none blinking': isLeadLocalitiesLoading }" ResizableDropdown
                            [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                            formControlName="Localities">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <ng-container *ngIf="globalSettingsData?.isCustomLeadFormEnabled">
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Sub-Community</div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="subCommunities"
                                [ngClass]="{ 'pe-none blinking': isLeadSubCommunitiesIsLoading }" ResizableDropdown
                                [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                                formControlName="SubCommunities">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Community</div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="communities"
                                [ngClass]="{ 'pe-none blinking': isLeadCommunitiesIsLoading }" ResizableDropdown
                                [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                                formControlName="Communities">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Tower Name</div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="towerNames"
                                [ngClass]="{ 'pe-none blinking': isLeadTowerNamesLoading }" ResizableDropdown
                                [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                                formControlName="TowerNames">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                </ng-container>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">City</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="cities"
                            [ngClass]="{ 'pe-none blinking': isLeadCitiesLoading }" ResizableDropdown [multiple]="true"
                            [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                            formControlName="Cities">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">State</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="states"
                            [ngClass]="{ 'pe-none blinking': isLeadStatesLoading }" ResizableDropdown [multiple]="true"
                            [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                            formControlName="States">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Country</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="countries"
                            [ngClass]="{ 'pe-none blinking': isLeadCountriesLoading }" ResizableDropdown
                            [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                            formControlName="Countries">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <!-- <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Postal Code</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="postalCodeList"
                            [ngClass]="{ 'pe-none blinking': isPostalCodeLoading }" ResizableDropdown [multiple]="true"
                            [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                            formControlName="PostalCodes">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div> -->
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Zone</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="zones"
                            [ngClass]="{ 'pe-none blinking': isLeadZonesLoading }" ResizableDropdown [multiple]="true"
                            [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                            formControlName="Zones">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Latitude</div>
                    <div class="mr-20 input-sm">
                        <form-errors-wrapper>
                            <input type="text" class="w-100" formControlName="Latitude"
                                placeholder="ex. 12.9209914"></form-errors-wrapper>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Longitude</div>
                    <div class="mr-20 input-sm">
                        <form-errors-wrapper>
                            <input type="text" class="w-100" formControlName="Longitude"
                                placeholder="ex. 77.6428506"></form-errors-wrapper>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Radius<small> (in kilometer)</small></div>
                    <div class="mr-20 input-sm">
                        <form-errors-wrapper>
                            <input type="text" formControlName="RadiusInKm" class="w-100"
                                placeholder="ex. 5"></form-errors-wrapper>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Country Code</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="countryCodes"
                            [ngClass]="{ 'pe-none blinking': isLeadCountryCodeLoading }" ResizableDropdown
                            [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                            formControlName="CountryCode">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Alt Country Code</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="altCountryCodes"
                            [ngClass]="{ 'pe-none blinking': isLeadAltCountryCodeLoading }" ResizableDropdown
                            [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                            formControlName="AltCountryCode">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
            <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Budget & Area:</legend>
            <div class="d-flex w-100 flex-wrap ng-select-sm">
                <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'GLOBAL.min' | translate}} {{'LABEL.budget' | translate}}</div>
                    <div class="w-100 align-center">
                        <div class="w-70 position-relative no-input-validation input-sm">
                            <form-errors-wrapper>
                                <div class="w-100 d-flex">
                                    <div class="w-50">
                                        <input type="number" (keydown)="onlyNumbers($event)" (input)="minBudgetCheck()"
                                            formControlName="FromMinBudget" id="inpFromMinBudget"
                                            data-automate-id="inpFromMinBudget" min="0" placeholder="ex. 123">
                                    </div>
                                    <h6 class="text-sm text-mud align-center m-4">To</h6>
                                    <div class="w-50">
                                        <input type="number" (keydown)="onlyNumbers($event)" min="0"
                                            (input)="minBudgetCheck()" formControlName="ToMinBudget" id="inpToMinBudget"
                                            data-automate-id="inpToMinBudget" placeholder="ex. 123">
                                    </div>
                                </div>
                            </form-errors-wrapper>
                            <div *ngIf="getFormValue('FromMinBudget')" class="position-absolute left-10 top-32">
                                <span class="text-nowrap text-xs text-accent-green fw-semi-bold">{{
                                    formatBudget(getFormValue('FromMinBudget'),getFormValue('Currency') ||
                                    defaultCurrency)}}</span>
                            </div>
                            <div *ngIf="getFormValue('ToMinBudget')" class="position-absolute right-10 top-32">
                                <span class="text-nowrap text-xs text-accent-green fw-semi-bold">{{
                                    formatBudget(getFormValue('ToMinBudget'),getFormValue('Currency') ||
                                    defaultCurrency)}}</span>
                            </div>
                        </div>
                        <div class="text-xs mt-60 text-red fw-semi-bold position-absolute"
                            *ngIf="( getFormValue('FromMinBudget') && getFormValue('ToMinBudget')) && !minBudgetValidation ">
                            {{'LEADS.budget-validation' | translate}}</div>
                        <div class="w-30 ml-8 mr-20">
                            <form-errors-wrapper label="Currency">
                                <ng-container *ngIf="leadCurrency?.length > 1; else showCurrencySymbol">
                                    <ng-select [virtualScroll]="true" formControlName="Currency" placeholder="ex.INR"
                                        [items]="leadCurrency" class="manage-dropdown"
                                        [ngClass]="{'pe-none blinking': isLeadCurrencyListIsLoading}"
                                        ResizableDropdown></ng-select>
                                </ng-container>
                                <ng-template #showCurrencySymbol>
                                    <h5 class="rupees px-12 py-4 fw-600 m-4">{{ defaultCurrency }}</h5>
                                </ng-template>
                            </form-errors-wrapper>
                        </div>
                    </div>
                </div>
                <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'GLOBAL.max' | translate}} {{'LABEL.budget' | translate}}</div>
                    <div class="w-100 align-center">
                        <div class="w-70 position-relative no-input-validation input-sm">
                            <form-errors-wrapper>
                                <div class="w-100 d-flex">
                                    <div class="w-50">
                                        <input type="number" (keydown)="onlyNumbers($event)" min="0"
                                            (input)="maxBudgetCheck()" formControlName="FromMaxBudget"
                                            id="inpFromMaxBudget" data-automate-id="inpFromMaxBudget"
                                            placeholder="ex. 123">
                                    </div>
                                    <h6 class="text-sm text-mud align-center m-4">To</h6>
                                    <div class="w-50">
                                        <input type="number" (keydown)="onlyNumbers($event)" min="0"
                                            (input)="maxBudgetCheck()" formControlName="ToMaxBudget" id="inpToMaxBudget"
                                            data-automate-id="inpToMaxBudget" placeholder="ex. 123">
                                    </div>
                                </div>
                            </form-errors-wrapper>
                            <div *ngIf="getFormValue('FromMaxBudget')" class="position-absolute left-10 top-32">
                                <span class="text-nowrap text-xs text-accent-green fw-semi-bold">{{
                                    formatBudget(getFormValue('FromMaxBudget'),getFormValue('Currency') ||
                                    defaultCurrency)}}</span>
                            </div>
                            <div *ngIf="getFormValue('ToMaxBudget')" class="position-absolute right-10 top-32">
                                <span class="text-nowrap text-xs text-accent-green fw-semi-bold">{{
                                    formatBudget(getFormValue('ToMaxBudget'),getFormValue('Currency') ||
                                    defaultCurrency)}}</span>
                            </div>
                        </div>
                        <div class="text-xs mt-60 text-red fw-semi-bold position-absolute"
                            *ngIf="( getFormValue('FromMaxBudget') && getFormValue('ToMaxBudget')) && !maxBudgetValidation ">
                            {{'LEADS.budget-validation' | translate}}</div>
                        <div class="w-30 ml-8 mr-20">
                            <form-errors-wrapper label="Currency">
                                <ng-container *ngIf="leadCurrency?.length > 1; else showCurrencySymbol">
                                    <ng-select [virtualScroll]="true" formControlName="Currency" placeholder="ex.INR"
                                        [items]="leadCurrency" class="manage-dropdown"
                                        [ngClass]="{'pe-none blinking': isLeadCurrencyListIsLoading}"
                                        ResizableDropdown></ng-select>
                                </ng-container>
                                <ng-template #showCurrencySymbol>
                                    <h5 class="rupees px-12 py-4 fw-600 m-4">{{ defaultCurrency }}</h5>
                                </ng-template>
                            </form-errors-wrapper>
                        </div>
                    </div>
                </div>
                <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Carpet Area</div>
                    <div class="w-100 align-center">
                        <div class="w-60pr no-input-validation input-sm">
                            <form-errors-wrapper>
                                <div class="w-100 d-flex">
                                    <div class="w-50">
                                        <input type="number" (input)="validateCarpetArea()"
                                            (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                            formControlName="MinCarpetArea" id="inpMinCarpetArea"
                                            data-automate-id="inpMinCarpetArea" min="0" placeholder="ex. 123">
                                    </div>
                                    <h6 class="text-sm text-mud align-center m-4">To</h6>
                                    <div class="w-50">
                                        <input type="number"
                                            (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                            (input)="validateCarpetArea()" min="0" formControlName="MaxCarpetArea"
                                            id="inpMaxCarpetArea" data-automate-id="inpMaxCarpetArea"
                                            placeholder="ex. 123">
                                    </div>
                                </div>
                            </form-errors-wrapper>
                        </div>
                        <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
                            *ngIf="( getFormValue('MinCarpetArea') && getFormValue('MaxCarpetArea')) && !carpetAreaValidations ">
                            {{'PROPERTY.area-validation' | translate}}</div>
                        <div class="w-40pr ml-8 mr-20">
                            <form-errors-wrapper label="{{'PROJECTS.size-unit' | translate}}">
                                <ng-select [virtualScroll]="true" formControlName="CarpetAreaUnitId" tabindex="4"
                                    placeholder="ex. sq. feet." [items]="areaSizeUnits"
                                    [ngClass]="{'pe-none blinking': isAreaUnitsLoading}" bindValue="id" bindLabel="unit"
                                    ResizableDropdown></ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                </div>
                <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Built-Up Area</div>
                    <div class="w-100 align-center">
                        <div class="w-60pr no-input-validation input-sm">
                            <form-errors-wrapper>
                                <div class="w-100 d-flex">
                                    <div class="w-50">
                                        <input type="number"
                                            (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                            (input)="validateBuildUpArea()" formControlName="MinBuiltUpArea"
                                            id="inpMinBuitUpArea" min="0" data-automate-id="inpPropSize"
                                            placeholder="ex. 123">
                                    </div>
                                    <h6 class="text-sm text-mud align-center m-4">To</h6>
                                    <div class="w-50">
                                        <input type="number"
                                            (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                            (input)="validateBuildUpArea()" min="0" formControlName="MaxBuiltUpArea"
                                            id="inpPropSize" data-automate-id="inpPropSize" placeholder="ex. 123">
                                    </div>
                                </div>
                            </form-errors-wrapper>
                        </div>
                        <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
                            *ngIf="( getFormValue('MinBuiltUpArea') && getFormValue('MaxBuiltUpArea')) && !buildUpAreaValidations ">
                            {{'PROPERTY.area-validation' | translate}}</div>
                        <div class="w-40pr ml-8 mr-20">
                            <form-errors-wrapper label="{{'PROJECTS.size-unit' | translate}}">
                                <ng-select [virtualScroll]="true" formControlName="BuiltUpAreaUnitId" tabindex="4"
                                    placeholder="ex. sq. feet." [items]="areaSizeUnits"
                                    [ngClass]="{'pe-none blinking': isAreaUnitsLoading}" bindValue="id" bindLabel="unit"
                                    ResizableDropdown></ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                </div>
                <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Saleable Area</div>
                    <div class="w-100 align-center">
                        <div class="w-60pr no-input-validation input-sm">
                            <form-errors-wrapper>
                                <div class="w-100 d-flex">
                                    <div class="w-50">
                                        <input type="number"
                                            (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                            (input)="saleableAreaValidation()" min="0"
                                            [max]="getFormValue('MaxSaleableArea')" formControlName="MinSaleableArea"
                                            id="inpMinSaleableArea" data-automate-id="inpMinSaleableArea"
                                            placeholder="ex. 123">
                                    </div>
                                    <h6 class="text-sm text-mud align-center m-4">To</h6>
                                    <div class="w-50">
                                        <input type="number"
                                            (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                            (input)="saleableAreaValidation()" min="0" formControlName="MaxSaleableArea"
                                            id="inpMaxSaleableArea" data-automate-id="inpMaxSaleableArea"
                                            placeholder="ex. 123">
                                    </div>
                                </div>
                            </form-errors-wrapper>
                        </div>
                        <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
                            *ngIf="( getFormValue('MinSaleableArea') && getFormValue('MaxSaleableArea')) && !saleableValidation">
                            {{'PROPERTY.area-validation' | translate}}</div>
                        <div class="w-40pr ml-8 mr-20">
                            <form-errors-wrapper label="{{'PROJECTS.size-unit' | translate}}">
                                <ng-select [virtualScroll]="true" formControlName="SaleableAreaUnitId" tabindex="4"
                                    placeholder="ex. sq. feet." [items]="areaSizeUnits"
                                    [ngClass]="{'pe-none blinking': isAreaUnitsLoading}" bindValue="id" bindLabel="unit"
                                    ResizableDropdown></ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                </div>
                <ng-container *ngIf="globalSettingsData?.isCustomLeadFormEnabled">
                    <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Property Area</div>
                        <div class="w-100 align-center">
                            <div class="w-60pr no-input-validation input-sm">
                                <form-errors-wrapper>
                                    <div class="w-100 d-flex">
                                        <div class="w-50">
                                            <input type="number"
                                                (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                                (input)="propertyAreaValidation()" min="0"
                                                [max]="getFormValue('MaxPropertyArea')"
                                                formControlName="MinPropertyArea" id="inpMinPropertyArea"
                                                data-automate-id="inpMinPropertyArea" placeholder="ex. 123">
                                        </div>
                                        <h6 class="text-sm text-mud align-center m-4">To</h6>
                                        <div class="w-50">
                                            <input type="number"
                                                (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                                (input)="propertyAreaValidation()" min="0"
                                                formControlName="MaxPropertyArea" id="inpMaxPropertyArea"
                                                data-automate-id="inpMaxPropertyArea" placeholder="ex. 123">
                                        </div>
                                    </div>
                                </form-errors-wrapper>
                            </div>
                            <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
                                *ngIf="( getFormValue('MinPropertyArea') && getFormValue('MaxPropertyArea')) && !areaValidation">
                                {{'PROPERTY.area-validation' | translate}}</div>
                            <div class="w-40pr ml-8 mr-20">
                                <form-errors-wrapper label="{{'PROJECTS.size-unit' | translate}}">
                                    <ng-select [virtualScroll]="true" formControlName="PropertyAreaUnitId" tabindex="4"
                                        placeholder="ex. sq. feet." [items]="areaSizeUnits"
                                        [ngClass]="{'pe-none blinking': isAreaUnitsLoading}" bindValue="id"
                                        bindLabel="unit" ResizableDropdown></ng-select>
                                </form-errors-wrapper>
                            </div>
                        </div>
                    </div>
                    <div class="flex-column w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Net Area</div>
                        <div class="w-100 align-center">
                            <div class="w-60pr no-input-validation input-sm">
                                <form-errors-wrapper>
                                    <div class="w-100 d-flex">
                                        <div class="w-50">
                                            <input type="number"
                                                (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                                (input)="netAreaValidation()" min="0" [max]="getFormValue('MaxNetArea')"
                                                formControlName="MinNetArea" id="inpMinNetArea"
                                                data-automate-id="inpMinNetArea" placeholder="ex. 123">
                                        </div>
                                        <h6 class="text-sm text-mud align-center m-4">To</h6>
                                        <div class="w-50">
                                            <input type="number"
                                                (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                                (input)="netAreaValidation()" min="0" formControlName="MaxNetArea"
                                                id="inpMaxNetArea" data-automate-id="inpMaxNetArea"
                                                placeholder="ex. 123">
                                        </div>
                                    </div>
                                </form-errors-wrapper>
                            </div>
                            <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
                                *ngIf="( getFormValue('MinNetArea') && getFormValue('MaxNetArea')) && !netValidation">
                                {{'PROPERTY.area-validation' | translate}}</div>
                            <div class="w-40pr ml-8 mr-20">
                                <form-errors-wrapper label="{{'PROJECTS.size-unit' | translate}}">
                                    <ng-select [virtualScroll]="true" formControlName="NetAreaUnitId" tabindex="4"
                                        placeholder="ex. sq. feet." [items]="areaSizeUnits"
                                        [ngClass]="{'pe-none blinking': isAreaUnitsLoading}" bindValue="id"
                                        bindLabel="unit" ResizableDropdown></ng-select>
                                </form-errors-wrapper>
                            </div>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Unit Number/Name</div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="unitNames"
                                [ngClass]="{ 'pe-none blinking': isLeadUnitNameLoading }" ResizableDropdown
                                [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                                formControlName="UnitNames">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                </ng-container>
            </div>
        </fieldset>
        <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
            <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Referral:</legend>
            <div class="d-flex w-100 flex-wrap ng-select-sm">
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'LEAD_FORM.referral-name' | translate}}</div>
                    <div class="mr-20 input-sm">
                        <form-errors-wrapper>
                            <input type="text" formControlName="ReferralName" class="w-100"
                                placeholder="enter referral name"></form-errors-wrapper>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">
                        {{'LEAD_FORM.referral-phone-no' | translate}}</div>
                    <div class="mr-20 input-sm"><form-errors-wrapper>
                            <input type="number" formControlName="ReferralContactNo" class="w-100"
                                placeholder="enter referral phone no"></form-errors-wrapper>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Referral Email</div>
                    <div class="mr-20 input-sm">
                        <form-errors-wrapper>
                            <input type="text" formControlName="ReferralEmail" class="w-100"
                                placeholder="enter referral email"></form-errors-wrapper>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Landline Number</div>
                    <div class="mr-20 input-sm">
                        <form-errors-wrapper>
                            <ng-select [virtualScroll]="true" [items]="landLineList" [multiple]="true"
                                [closeOnSelect]="false" ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}"
                                formControlName="LandLine" [ngClass]="{'pe-none blinking': isLandLineLoading}">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item}}</span>
                                    </div>
                                </ng-template>
                            </ng-select></form-errors-wrapper>
                    </div>
                </div>
            </div>
        </fieldset>
        <ng-container *ngIf="currentPath === '/invoice'">
            <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
                <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Booking Details:</legend>
                <div class="d-flex flex-wrap ng-select-sm">
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Book under name</div>
                        <div class="mr-20 input-sm"><form-errors-wrapper>
                                <input type="text" class="w-100" formControlName="BookedUnderName"
                                    placeholder="ex. abid"></form-errors-wrapper>
                        </div>
                    </div>
                    <!-- <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
            <div class="field-label-req">Booked Date</label>
            <input [owlDateTime]="dt1" [owlDateTimeTrigger]="dt1" readonly id="inpBookDateTime"
            data-automate-id="inpBookDateTime"
            placeholder="ex. 5/03/2025, 12:00 pm">
          <owl-date-time #dt1 [hour12Timer]="'true'"></owl-date-time>
          </div> -->
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Agreement Value</div>
                        <div class="mr-20 input-sm"><form-errors-wrapper>
                                <input type="number" class="w-100" formControlName="LowerAgreementLimit"
                                    placeholder="ex. 4000000"></form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Car Parking Charges</div>
                        <div class="mr-20 input-sm"><form-errors-wrapper>
                                <input type="number" formControlName="CarParkingCharges" class="w-100"
                                    placeholder="ex. 4000000"></form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Add-on Charges</div>
                        <div class="mr-20 input-sm"><form-errors-wrapper>
                                <input type="number" formControlName="AdditionalCharges" class="w-100"
                                    placeholder="ex. 4000000"></form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Total Sold Price</div>
                        <div class="mr-20 input-sm"><form-errors-wrapper>
                                <input type="number" class="w-100" formControlName="SoldPrice"
                                    placeholder="ex. 4000000"></form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Payment Mode</div>
                        <div class="mr-20 input-sm"><form-errors-wrapper>
                                <ng-select [virtualScroll]="true" [closeOnSelect]="true" ResizableDropdown
                                    placeholder="select" [multiple]="false" bindLabel="label" bindValue="value"
                                    formControlName='PaymentMode' [items]="PaymentModeList">
                                </ng-select></form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Discount</div>
                        <div class="mr-20 input-sm"><form-errors-wrapper>
                                <input type="number" class="w-100" formControlName="LowerDiscountLimit"
                                    placeholder="ex. 4000000"></form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Balance Amount</div>
                        <div class="mr-20 input-sm"><form-errors-wrapper>
                                <input type="number" class="w-100" formControlName="RemainingAmount"
                                    placeholder="ex. 4000000"></form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Type of Payment Selected</div>
                        <div class="mr-20 input-sm">
                            <ng-select [virtualScroll]="true" [closeOnSelect]="true" ResizableDropdown
                                placeholder="select" [multiple]="false" bindLabel="label" bindValue="value"
                                formControlName="PaymentType" [items]="PaymentTypeList">
                            </ng-select>
                        </div>
                    </div>
                    <!-- --------------------- Brockrage Charge --------------------- -->
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Brokerage Charges</div>
                        <div class="mr-20 input-sm"><form-errors-wrapper>
                                <input type="number" class="w-100" formControlName="BrokerageCharges"
                                    placeholder="ex. 4000000"></form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Net Brokerage Amount</div>
                        <div class="mr-20 input-sm"><form-errors-wrapper>
                                <input type="number" class="w-100" formControlName="NetBrokerageAmount"
                                    placeholder="ex. 4000000"></form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">GST</div>
                        <div class="mr-20 input-sm">
                            <form-errors-wrapper>
                                <input type="number" class="w-100" placeholder="ex. 10%" formControlName="Gst" min="1"
                                    max="99">
                                <div *ngIf="filtersForm.get('Gst')?.errors?.min || filtersForm.get('Gst')?.errors?.max"
                                    class="text-danger text-xs position-absolute right-5">
                                    GST must be between 1 and 99.
                                </div>
                            </form-errors-wrapper>
                        </div>

                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Total Brokerage</div>
                        <div class="mr-20 input-sm"><form-errors-wrapper>
                                <input type="number" class="w-100" formControlName="TotalBrokerage"
                                    placeholder="ex. 4000000"></form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Brokerage Earned</div>
                        <div class="mr-20 input-sm"><form-errors-wrapper>
                                <input type="number" class="w-100" formControlName="EarnedBrokerage"
                                    placeholder="ex. 4000000"></form-errors-wrapper>
                        </div>
                    </div>
                </div>
            </fieldset>
        </ng-container>
        <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
            <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">User:</legend>
            <div class="d-flex w-100 flex-wrap ng-select-sm">
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Booked By</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="reportees"
                            [ngClass]="{ 'pe-none blinking': isReporteesIsLoading }" ResizableDropdown [multiple]="true"
                            [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName"
                            bindValue="id" formControlName="bookedByIds">
                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="flex-between">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.firstName}}
                                            {{item.lastName}}</span></div>
                                    <span class="text-disabled" *ngIf="!item.isActive">( Disabled)</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'LEADS.created-by' | translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="reportees"
                            [ngClass]="{ 'pe-none blinking': isReporteesIsLoading }" ResizableDropdown [multiple]="true"
                            [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName"
                            bindValue="id" formControlName="createdByIds">
                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="flex-between">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.firstName}}
                                            {{item.lastName}}</span></div>
                                    <span class="text-disabled" *ngIf="!item.isActive">( Disabled)</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'LEADS.last-modified-by' | translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="reportees"
                            [ngClass]="{ 'pe-none blinking': isReporteesIsLoading }" ResizableDropdown [multiple]="true"
                            [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName"
                            bindValue="id" formControlName="lastModifiedByIds">
                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="flex-between">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.firstName}}
                                            {{item.lastName}}</span></div>
                                    <span class="text-disabled" *ngIf="!item.isActive">( Disabled)</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'DASHBOARD.deleted' | translate}} {{'GLOBAL.by' | translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="reportees"
                            [ngClass]="{ 'pe-none blinking': isReporteesIsLoading }" ResizableDropdown [multiple]="true"
                            [closeOnSelect]="false" [dropdownPosition]="'top'"
                            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName" bindValue="id"
                            formControlName="archivedByIds">
                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="flex-between">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.firstName}}
                                            {{item.lastName}}</span></div>
                                    <span class="text-disabled" *ngIf="!item.isActive">( Disabled)</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'LEADS.restored-by' | translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="reportees"
                            [ngClass]="{ 'pe-none blinking': isReporteesIsLoading }" ResizableDropdown [multiple]="true"
                            [closeOnSelect]="false" [dropdownPosition]="'top'"
                            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName" bindValue="id"
                            formControlName="restoredByIds">
                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="flex-between">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.firstName}}
                                            {{item.lastName}}</span></div>
                                    <span class="text-disabled" *ngIf="!item.isActive">( Disabled)</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'LEADS.sourcing-manager' | translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" ResizableDropdown [items]="allUsers"
                            [ngClass]="{ 'pe-none blinking': isUsersListForReassignmentIsLoading }" [multiple]="true"
                            [closeOnSelect]="false" [dropdownPosition]="'top'"
                            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName" bindValue="id"
                            formControlName="sourcingManagers">
                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="flex-between">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.firstName}}
                                            {{item.lastName}}</span></div>
                                    <span class="text-disabled" *ngIf="!item.isActive">( Disabled)</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'LEADS.closing-manager' | translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" ResizableDropdown [items]="allUsers"
                            [ngClass]="{ 'pe-none blinking': isUsersListForReassignmentIsLoading }" [multiple]="true"
                            [closeOnSelect]="false" [dropdownPosition]="'top'"
                            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName" bindValue="id"
                            formControlName="closingManagers">
                            <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                <span class="ng-value-label"> {{item.firstName + ' ' +
                                    item.lastName}}</span>
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="flex-between">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.firstName}}
                                            {{item.lastName}}</span></div>
                                    <span class="text-disabled" *ngIf="!item.isActive">( Disabled)</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="border rounded-3 pb-20 pl-20 mt-24 mr-20">
            <legend class="text-accent-green float-none w-auto header-4 fw-600 mb-0 px-8">Others:</legend>
            <div class="d-flex w-100 flex-wrap ng-select-sm">
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'LEAD_FORM.enquired-for'| translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="enquiryType" [multiple]="true"
                            [closeOnSelect]="false" formControlName="enquiredFor" ResizableDropdown
                            placeholder="{{'GLOBAL.select' | translate}}" [bindLabel]="'type'" [bindValue]="'type'">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span
                                        class="text-truncate-1 break-all">{{item.type}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Purpose</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="purposeList" [multiple]="true"
                            [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                            bindLabel="displayName" formControlName="Purposes" bindValue="displayName"
                            ResizableDropdown>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span
                                        class="text-truncate-1 break-all">{{item.displayName}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'INTEGRATION.agency-name' | translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" ResizableDropdown [items]="agencyNameList"
                            [ngClass]="{ 'pe-none blinking': isAgencyNameListLoading }" [multiple]="true"
                            formControlName="AgencyNames" [closeOnSelect]="false"
                            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="item" bindValue="item">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Channel Partner Name</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" ResizableDropdown [items]="channelPartnerList"
                            [ngClass]="{ 'pe-none blinking': isChannelPartnerLoading }" [multiple]="true"
                            formControlName="ChannelPartnerNames" [closeOnSelect]="false"
                            placeholder="{{'GLOBAL.select' | translate}}" bindLabel="item" bindValue="item">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'LEAD_FORM.campaign-name' | translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" ResizableDropdown [items]="campaigns" [multiple]="true"
                            [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}" bindLabel="item"
                            bindValue="item" formControlName="CampaignNames" [dropdownPosition]="'top'">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Excel Sheet</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [multiple]="false"
                            [ngClass]="{ 'pe-none blinking': isUploadTypeNameListIsLoading }" [closeOnSelect]="true"
                            ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}"
                            formControlName="UploadTypeName">
                            <ng-option *ngFor="let file of uploadTypeList" [value]="file">
                                {{file}}</ng-option>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Is Untouched</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [multiple]="false" [closeOnSelect]="true" ResizableDropdown
                            placeholder="{{'GLOBAL.select' | translate}}" formControlName="IsUntouched">
                            <ng-option *ngFor="let flag of isUntouched" [value]="flag.value">
                                {{flag?.name}}</ng-option>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Data Converted</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [multiple]="false" [closeOnSelect]="true" ResizableDropdown
                            placeholder="{{'GLOBAL.select' | translate}}" formControlName="DataConverted">
                            <ng-option *ngFor="let flag of isUntouched" [value]="flag.value">
                                {{flag?.name}}</ng-option>
                        </ng-select>
                    </div>
                </div>
                <div *ngIf="getFormValue('DataConverted')" class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Qualified By</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="allUsersList"
                            [ngClass]="{ 'pe-none blinking': isUsersListForReassignmentIsLoading }" ResizableDropdown
                            [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                            bindLabel="fullName" bindValue="id" formControlName="QualifiedByIds">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected">
                                    <span class="checkmark"></span><span class="text-truncate-1 break-all">
                                        {{item.fullName}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Designation</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="designationList"
                            [ngClass]="{ 'pe-none blinking': isUsersByDesignationIsLoading }" ResizableDropdown
                            [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                            bindLabel="name" bindValue="id" formControlName="designationsId">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="flex-between">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item.name}}</span></div>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">{{'LEADS.profession' | translate}}</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="profession" [multiple]="true" [closeOnSelect]="false"
                            ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" bindLabel="item"
                            bindValue="item" formControlName="Profession">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span class="text-truncate-1 break-all">{{item}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <ng-container *ngIf="globalSettingsData?.isCustomLeadFormEnabled">
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Nationality</div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="nationalities"
                                [ngClass]="{ 'pe-none blinking': isLeadNationalityLoading }" ResizableDropdown
                                [multiple]="true" [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                                formControlName="Nationality">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                </ng-container>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Facebook Properties</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [multiple]="false" [closeOnSelect]="true" ResizableDropdown
                            placeholder="{{'GLOBAL.select' | translate}}"
                            [ngClass]="{ 'pe-none blinking': isAdditionalPropertyIsLoading }"
                            formControlName="AdditionalPropertiesKey" (change)="fbPropertyChange($event)">
                            <ng-option *ngFor="let key of additionalProperty" [value]="key">
                                {{key}}</ng-option>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100" *ngIf="getFormValue('AdditionalPropertiesKey')">
                    <div class="field-label">Fb {{getFormValue('AdditionalPropertiesKey')}} Value</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" formControlName="AdditionalPropertiesValue"
                            [ngClass]="{ 'pe-none blinking': isAdditionalPropertyValueIsLoading }" [multiple]="false"
                            [closeOnSelect]="true" ResizableDropdown [dropdownPosition]="'top'"
                            placeholder="{{'GLOBAL.select' | translate}}">
                            <ng-option *ngFor="let value of additionalPropertyValue" [value]="value">
                                {{ value }}
                            </ng-option>
                        </ng-select>
                    </div>
                </div>
                <ng-container *ngIf="globalSettingsData?.isCustomLeadFormEnabled">
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                        <div class="field-label">Cluster Name</div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="clusterNames" [multiple]="true"
                                [closeOnSelect]="false" ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}"
                                bindLabel="item" bindValue="item" formControlName="ClusterName"
                                [dropdownPosition]="'top'">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span
                                            class="text-truncate-1 break-all">{{item}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="w-25 tb-w-33 ip-w-50 ph-w-100"
                        *ngIf="permission?.has('Permissions.Leads.ViewConfidentialNotes')">
                        <div class="field-label">Confidential Notes</div>
                        <div class="mr-20 input-sm">
                            <form-errors-wrapper>
                                <input type="text" formControlName="ConfidentialNotes" class="w-100"
                                    placeholder="enter notes"></form-errors-wrapper>
                        </div>
                    </div>
                </ng-container>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Lead Filter Type</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="leadFilterTypes" [multiple]="true"
                            [closeOnSelect]="false" ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}"
                            bindLabel="displayName" bindValue="value" formControlName="LeadType"
                            [dropdownPosition]="'top'">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark"></span><span
                                        class="text-truncate-1 break-all">{{item.displayName}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div *ngIf="isShowParentLead" class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Show Child Count</div>
                    <div class="mr-20 input-sm">
                        <form-errors-wrapper>
                            <input type="number" formControlName="ChildLeadsCount" class="w-100"
                                placeholder="enter count"></form-errors-wrapper>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Call Direction</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="callDirections" [multiple]="true"
                            [closeOnSelect]="false" ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}"
                            bindLabel="displayName" bindValue="value" formControlName="CallDirections"
                            [dropdownPosition]="'top'">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container">
                                    <input type="checkbox" id="call-direction-{{index}}"
                                        data-automate-id="call-direction-{{index}}" [checked]="item$.selected">
                                    <span class="checkmark"></span>
                                    <span class="text-truncate-1 break-all">{{item.displayName}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Call Status</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="callStatuses" [multiple]="true"
                            [closeOnSelect]="false" ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}"
                            bindLabel="displayName" bindValue="value" formControlName="CallStatuses"
                            [dropdownPosition]="'top'">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container">
                                    <input type="checkbox" id="call-status-{{index}}"
                                        data-automate-id="call-status-{{index}}" [checked]="item$.selected">
                                    <span class="checkmark"></span>
                                    <span class="text-truncate-1 break-all">{{item.displayName}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>

                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Call Made By</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [items]="reportees" [multiple]="true" [closeOnSelect]="false"
                            ResizableDropdown placeholder="{{'GLOBAL.select' | translate}}" bindLabel="fullName"
                            bindValue="id" formControlName="UserIds" [dropdownPosition]="'top'">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container">
                                    <input type="checkbox" id="call-made-by-{{index}}"
                                        data-automate-id="call-made-by-{{index}}" [checked]="item$.selected">
                                    <span class="checkmark"></span>
                                    <span class="text-truncate-1 break-all">{{item.firstName}}
                                        {{item.lastName}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50">
                    <div class="field-label">Call Date Filter</div>
                    <div class="d-flex">
                        <div class="mr-10 w-45">
                            <ng-select [virtualScroll]="true" [(ngModel)]="selectedCallDateType"
                                (ngModelChange)="onCallDateTypeChange($event)" [ngModelOptions]="{standalone: true}"
                                [dropdownPosition]="'top'" [items]="callDateFilter" tabindex="4" [searchable]="false"
                                placeholder="ex. Yesterday" ResizableDropdown>
                            </ng-select>
                        </div>
                        <div class="form-group w-50">
                            <div [owlDateTimeTrigger]="dt4"
                                [ngClass]="{'pe-none': selectedCallDateType !== 'Custom Date'}"
                                class="date-picker border br-6 align-center mr-8 ip-mb-10">

                                <input type="text" [owlDateTimeTrigger]="dt4" [owlDateTime]="dt4" [selectMode]="'range'"
                                    [(ngModel)]="callDate" [ngModelOptions]="{standalone: true}"
                                    placeholder="ex. 19-06-2025 - 29-06-2025" class="h-32 text-large" />

                                <owl-date-time [pickerType]="'calendar'" #dt4
                                    (afterPickerOpen)="onPickerOpened(currentDate)">
                                </owl-date-time>

                            </div>
                        </div>
                    </div>
                </div>

                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Gender</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [multiple]="true" formControlName="genderTypes"
                            placeholder="ex. sq. feet." [closeOnSelect]="false" [items]="gender" bindLabel="displayName"
                            bindValue="id" dropdownPosition="top" ResizableDropdown>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container">
                                    <input type="checkbox" id="gender-{{index}}" data-automate-id="gender-{{index}}"
                                        [checked]="item$.selected">
                                    <span class="checkmark"></span>
                                    <span class="text-truncate-1 break-all">{{item.displayName}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Date of Birth</div>
                    <div class="mr-20 form-group">
                        <input type="text" class="h-32" formControlName="dateOfBirth" placeholder="23/04/2002"
                            [owlDateTimeTrigger]="dt3" [owlDateTime]="dt3" [max]="maxDate" />
                        <owl-date-time #dt3 (afterPickerOpen)="onPickerOpened(currentDate)"
                            [pickerType]="'calendar'"></owl-date-time>
                    </div>
                </div>
                <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                    <div class="field-label">Marital Status</div>
                    <div class="mr-20">
                        <ng-select [virtualScroll]="true" [multiple]="true" formControlName="maritalStatuses"
                            placeholder="ex. sq. feet." [closeOnSelect]="false" [items]="maritalStatus" bindLabel="name"
                            bindValue="value" dropdownPosition="top" ResizableDropdown>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container">
                                    <input type="checkbox" id="marital-status-{{index}}"
                                        data-automate-id="marital-status-{{index}}" [checked]="item$.selected">
                                    <span class="checkmark"></span>
                                    <span class="text-truncate-1 break-all">{{item.name}}</span>
                                </div>
                            </ng-template>
                        </ng-select>
                    </div>
                </div>

                <!-- <div class="align-center w-25 tb-w-33 ip-w-50 ph-w-100">
                    <label class="checkbox-container mb-4">
                        <input type="checkbox" [(ngModel)]="appliedFilter.withTeam">
                        <span class="checkmark"></span>Remove duplicates
                    </label>
                </div> -->
            </div>
        </fieldset>
    </div>
    <div class="flex-end py-20">
        <u class="mr-20 fw-semi-bold text-mud cursor-pointer"
            (click)="modalRef.hide(); trackingService.trackFeature('Web.Leads.Filter.Cancel.Click')">{{'BUTTONS.cancel'
            |
            translate }}</u>
        <div class="btn-gray mr-20" (click)="onClearAllFilters()">{{ 'GLOBAL.reset' | translate }}</div>
        <button class="btn-coal"
            [disabled]="getFormValue('AdditionalPropertiesKey') && !getFormValue('AdditionalPropertiesValue')"
            (click)="applyAdvancedFilter()">{{ 'GLOBAL.search' | translate }}</button>
    </div>
</div>