import { Component, EventEmitter, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { IntegrationSource, LeadSource } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { LeadSourceType } from 'src/app/core/interfaces/master-data.interface';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import { UpdateUserAssignment } from 'src/app/reducers/automation/automation.actions';
import { FetchTagsList } from 'src/app/reducers/custom-tags/custom-tags.actions';
import { getTagsList } from 'src/app/reducers/custom-tags/custom-tags.reducer';
import { FetchAgencyNameList } from 'src/app/reducers/Integration/integration.actions';
import { getAgencyNameList } from 'src/app/reducers/Integration/integration.reducer';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchCampaignList,
  FetchChannelPartnerList,
  FetchSubSourceList,
} from 'src/app/reducers/lead/lead.actions';
import {
  getCampaignList,
  getChannelPartnerList,
  getSubSourceList,
} from 'src/app/reducers/lead/lead.reducer';
import { updateAssignedProjects } from 'src/app/reducers/project/project.action';
import { UpdateQrForm } from 'src/app/reducers/qr-form/qr-form.action';

@Component({
  selector: 'configuration',
  templateUrl: './configuration.component.html',
})
export class ConfigurationComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  expandedSections: { [key: string]: boolean } = {};
  selectedTemplate: any;
  subSources: any[] = [];
  leadSources: LeadSourceType;
  subSourceList: any;
  leadTags: any[] = [];
  moduleId: string;
  updatedUser: boolean;
  assignedToUsers: FormControl = new FormControl(null);
  project: FormControl = new FormControl(null);
  allProjectsList: any[] = [];
  agencyNameList: any[] = [];
  updatedProjects: boolean;
  agencyName: FormControl = new FormControl(null);
  updatedAgent: boolean;
  trackCampaignForm: FormGroup;
  campaignList: any[] = [];
  channelPartnerList: any[] = [];
  selectedTags: any[] = [];
  constructor(
    public modalRef: BsModalRef,
    private store: Store<AppState>,
    private fb: FormBuilder
  ) { }

  ngOnInit(): void {
    this.store.dispatch(new FetchAgencyNameList());
    this.store.dispatch(new FetchChannelPartnerList());
    this.store.dispatch(new FetchCampaignList());
    this.store.dispatch(new FetchTagsList());
    this.store.dispatch(new FetchAllSources());
    this.store.dispatch(new FetchSubSourceList());

    this.trackCampaignForm = this.fb.group({
      name: [null, [Validators.required]],
      source: [null],
      subSource: [null],
      tags: [null],
      channelPartner: [null],
    });

    this.trackCampaignForm.controls['source']?.valueChanges.subscribe(
      (val: any) => {
        this.trackCampaignForm.patchValue({ subSource: null });
        this.updateSubSource(val);
      }
    );

    this.store
      .select(getChannelPartnerList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.channelPartnerList = item
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });
    this.store
      .select(getAgencyNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.agencyNameList = item
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.store
      .select(getAllSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((sources: any) => {
        this.leadSources = sources
          .filter((source: any) => source.isEnabled)
          .slice()
          .sort((a: any, b: any) =>
            a?.displayName.localeCompare(b?.displayName)
          ) || [];
          this.updateSubSources()
      });

    this.store
      .select(getSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.subSourceList = data;
        this.updateSubSources(null);
      });

    this.store
      .select(getCampaignList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.campaignList = data
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.store.select(getTagsList).subscribe((data: any) => {
      let flagData = data || [];
      if (flagData.length > 0) {
        this.leadTags = flagData?.filter((flag: any) => flag?.isActive);
        this.sortFlags();
      }
    });

    if (this.selectedTemplate.campaign) {
      const tagsDTO = this.selectedTemplate.campaign.flagIds
        ?.map((flagName: string) =>
          this.leadTags.find((tag: any) => tag.name === flagName)
        )
        .filter((tag: any) => tag);

      this.trackCampaignForm.patchValue({
        name: this.selectedTemplate.campaign?.name,
        source: LeadSource[this.selectedTemplate?.leadSource],
        subSource: this.selectedTemplate?.subSource,
        channelPartner: this.selectedTemplate?.channelPartnerName,
        tags: tagsDTO,
      });
    }
  }

  updateAgentName() {
    let payload = {
      ...this.selectedTemplate,
      agencyName: this.agencyName.value,
    };
    this.store.dispatch(new UpdateQrForm(payload, this.selectedTemplate.id));
    this.updatedAgent = true;
  }

  updateProject() {
    const projectsArray = this.project.value.map((proj: any) => proj);
    this.store.dispatch(
      new updateAssignedProjects(this.selectedTemplate.id, projectsArray)
    );
    this.updatedProjects = true;
  }

  updateUsers() {
    const selectedUsers = [...this.assignedToUsers.value];
    const payload = {
      entityId: this.selectedTemplate.id,
      userIds: [...selectedUsers],
      moduleId: this.moduleId,
    };
    this.store.dispatch(new UpdateUserAssignment(payload));
    this.updatedUser = true;
  }

  updateTrackCampaign(event: any) {
    if (!this.trackCampaignForm.valid) {
      validateAllFormFields(this.trackCampaignForm);
      return;
    }
    let payload = {
      ...this.selectedTemplate,
      campaignName: this.trackCampaignForm?.get('name').value,
      flagIds: this.trackCampaignForm
        ?.get('tags')
        .value?.map((tags: any) => tags?.id),
      leadSource: LeadSource[this.trackCampaignForm?.get('source').value],
      subSource: this.trackCampaignForm?.get('subSource').value,
      channelPartnerName: this.trackCampaignForm?.get('channelPartner').value,
    };
    this.store.dispatch(new UpdateQrForm(payload, this.selectedTemplate.id));
    this.toggleExpand('campaign', event);
  }

  toggleExpand(section: string, event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    this.expandedSections[section] = !this.expandedSections[section];
  }

  onSelectSource(source: any) {
    if (source) {
      if (typeof source === 'string') {
        this.updateSubSources(source);
      } else {
        this.updateSubSource(source.value);
      }
    } else {
      this.updateSubSources(null);
    }
  }

  updateSubSource(selectedSource?: any) {
    let sourceValue: number;
    if (typeof selectedSource === 'number') {
      sourceValue = selectedSource;
    } else {
      sourceValue = selectedSource ? Number(LeadSource[selectedSource]) : 0;
    }
    const sourceName: string = LeadSource[sourceValue];
    if (sourceName === '99 Acres') {
      this.subSources = this.subSourceList['NinetyNineAcres'] || [];
    } else {
      const formattedKey = sourceName?.replace(/\s+/g, '');
      if (Array.isArray(this.subSourceList[formattedKey])) {
        this.subSources = this.subSourceList[formattedKey] || [];
      } else {
        this.subSources = this.subSourceList[sourceName] || [];
      }
    }
  }

  updateSubSources(sourceName?: string | null) {
    if (sourceName) {
      if (sourceName === '99 Acres') {
        this.subSources = this.subSourceList['NinetyNineAcres'] || [];
      } else {
        const formattedKey = sourceName.replace(/\s+/g, '');
        if (Array.isArray(this.subSourceList[formattedKey])) {
          this.subSources = this.subSourceList[formattedKey] || [];
        } else {
          this.subSources = this.subSourceList[sourceName] || [];
        }
      }
    } else {
      let subSourceList: string[] = [];
      if (this.leadSources && Array.isArray(this.leadSources)) {
        for (const lead of this.leadSources) {
          if (lead?.displayName === '99 Acres') {
            const ninetyNineAcresSubSources = this.subSourceList['NinetyNineAcres'] || [];
            subSourceList.push(...ninetyNineAcresSubSources);
            continue;
          }
          const formattedKey = lead?.displayName?.replace(/\s+/g, '');
          let match = this.subSourceList[formattedKey];
          if (!match) {
            match = this.subSourceList[lead?.displayName];
          }
          if (!match && formattedKey?.toLowerCase() === '99acres') {
            match = this.subSourceList['NinetyNineAcres'];
          }
          if (Array.isArray(match)) {
            subSourceList.push(...match);
          }
        }
      }
      this.subSources = subSourceList;
    }
  }

  sortFlags() {
    const leadTag = [...this.leadTags];
    leadTag.sort((a, b) => {
      if (a.isActive === b.isActive) {
        const nameA = a.name.toUpperCase();
        const nameB = b.name.toUpperCase();
        return nameA.localeCompare(nameB);
      } else {
        return a.isActive ? -1 : 1;
      }
    });
    this.leadTags = leadTag;
  }
}
